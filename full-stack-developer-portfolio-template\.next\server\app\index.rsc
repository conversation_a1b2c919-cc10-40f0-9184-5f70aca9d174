1:"$Sreact.fragment"
2:I[5244,[],""]
3:I[3866,[],""]
4:I[7033,[],"ClientPageRoot"]
5:I[8930,["704","static/chunks/704-c1b7454acf2285d3.js","974","static/chunks/app/page-a4cc83484493c8d6.js"],"default"]
8:I[6213,[],"OutletBoundary"]
a:I[6213,[],"MetadataBoundary"]
c:I[6213,[],"ViewportBoundary"]
e:I[4835,[],""]
:HL["/_next/static/css/499f96a3048ebeb3.css","style"]
0:{"P":null,"b":"EKL56wr9iWaZ9v0w6zHUC","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/499f96a3048ebeb3.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[],[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L4",null,{"Component":"$5","searchParams":{},"params":{},"promises":["$@6","$@7"]}],null,["$","$L8",null,{"children":"$L9"}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","Mz7QqA4qzhgnkS5JJ9OqP",{"children":[["$","$La",null,{"children":"$Lb"}],["$","$Lc",null,{"children":"$Ld"}],null]}]]}],false]],"m":"$undefined","G":["$e","$undefined"],"s":false,"S":true}
6:{}
7:{}
d:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:[["$","meta","0",{"charSet":"utf-8"}],["$","title","1",{"children":"[Ben Basil Tomy] - Software Engineer"}],["$","meta","2",{"name":"description","content":"Welcome to my portfolio! I am a passionate full-stack developer who bridges the gap between frontend and backend development. Specializing in creating complete web solutions, from beautiful user interfaces to robust server architectures."}],["$","meta","3",{"name":"author","content":"[Ben Basil Tomy]"}],["$","meta","4",{"name":"keywords","content":"Full-Stack Developer,Software Engineer,Frontend Development,Backend Development,React,Next.js,Node.js,TypeScript,Database Design,API Development,Cloud Solutions,DevOps,System Architecture,Web Development,[Ben Basil Tomy]"}],["$","meta","5",{"name":"creator","content":"[Ben Basil Tomy]"}],["$","meta","6",{"name":"robots","content":"index, follow"}],["$","meta","7",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","8",{"property":"og:title","content":"[Ben Basil Tomy] - Full-Stack Developer Portfolio"}],["$","meta","9",{"property":"og:description","content":"Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack."}],["$","meta","10",{"property":"og:url","content":"https://your-domain.com"}],["$","meta","11",{"property":"og:site_name","content":"[Ben Basil Tomy] - Portfolio"}],["$","meta","12",{"property":"og:locale","content":"en_US"}],["$","meta","13",{"property":"og:image","content":"http://localhost:3000/og-image.jpg"}],["$","meta","14",{"property":"og:image:width","content":"1200"}],["$","meta","15",{"property":"og:image:height","content":"630"}],["$","meta","16",{"property":"og:image:alt","content":"[Ben Basil Tomy] - Full-Stack Developer Portfolio"}],["$","meta","17",{"property":"og:type","content":"website"}],["$","meta","18",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","19",{"name":"twitter:creator","content":"@yourusername"}],["$","meta","20",{"name":"twitter:title","content":"[Ben Basil Tomy] - Full-Stack Developer"}],["$","meta","21",{"name":"twitter:description","content":"Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack."}],["$","meta","22",{"name":"twitter:image","content":"http://localhost:3000/og-image.jpg"}],["$","link","23",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]]
9:null
