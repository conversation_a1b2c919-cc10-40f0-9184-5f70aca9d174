"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";

interface LoaderProps {
  onLoadingComplete: () => void;
}

export default function Loader({ onLoadingComplete }: LoaderProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onLoadingComplete, 500); // Wait for fade out animation
    }, 2500); // Show loader for 2.5 seconds

    return () => clearTimeout(timer);
  }, [onLoadingComplete]);

  if (!isVisible) {
    return (
      <motion.div
        initial={{ opacity: 1 }}
        animate={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="fixed inset-0 z-50 bg-gradient-to-b from-gray-950 to-black flex items-center justify-center"
      >
        <div className="relative">
          {/* Lightning Bolt */}
          <motion.div initial={{ scale: 0, rotate: -10 }} animate={{ scale: 1, rotate: 0 }} transition={{ duration: 0.6, ease: "easeOut" }} className="relative z-10">
            <svg width="300" height="400" viewBox="0 0 300 400" fill="none" xmlns="http://www.w3.org/2000/svg" className="drop-shadow-2xl">
              {/* Main Lightning Bolt */}
              <path
                d="M150 20 L180 20 L120 160 L180 160 L80 380 L120 380 L100 240 L160 240 L150 20 Z"
                fill="url(#lightning-gradient)"
                stroke="url(#lightning-border)"
                strokeWidth="2"
              />
              {/* Inner Lightning Glow */}
              <path
                d="M155 25 L175 25 L125 165 L170 165 L90 375 L115 375 L105 245 L155 245 L155 25 Z"
                fill="url(#inner-glow)"
                opacity="0.9"
              />
              {/* Lightning Core */}
              <path
                d="M160 30 L170 30 L130 170 L165 170 L95 370 L110 370 L110 250 L150 250 L160 30 Z"
                fill="rgba(255, 255, 255, 0.8)"
                opacity="0.7"
              />
              <defs>
                <linearGradient id="lightning-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="rgba(255, 255, 255, 0.95)" />
                  <stop offset="20%" stopColor="rgba(20, 184, 166, 0.9)" />
                  <stop offset="60%" stopColor="rgba(20, 184, 166, 0.7)" />
                  <stop offset="100%" stopColor="rgba(20, 184, 166, 0.4)" />
                </linearGradient>
                <linearGradient id="lightning-border" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
                  <stop offset="50%" stopColor="rgba(20, 184, 166, 1)" />
                  <stop offset="100%" stopColor="rgba(20, 184, 166, 0.6)" />
                </linearGradient>
                <linearGradient id="inner-glow" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="rgba(255, 255, 255, 0.7)" />
                  <stop offset="40%" stopColor="rgba(20, 184, 166, 0.6)" />
                  <stop offset="100%" stopColor="rgba(20, 184, 166, 0.2)" />
                </linearGradient>
              </defs>
            </svg>
          </motion.div>

          {/* Animated Gradient Rays */}
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute inset-0 pointer-events-none"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ 
                opacity: [0, 0.8, 0],
                scale: [0, 1.5, 2],
                rotate: i * 45
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeOut"
              }}
            >
              <div
                className="absolute top-1/2 left-1/2 w-3 h-48 -translate-x-1/2 -translate-y-1/2 origin-bottom"
                style={{
                  background: `linear-gradient(to top, 
                    rgba(20, 184, 166, 0.6) 0%, 
                    rgba(20, 184, 166, 0.3) 50%, 
                    transparent 100%)`
                }}
              />
            </motion.div>
          ))}

          {/* Central Glow */}
          <motion.div
            className="absolute inset-0 pointer-events-none"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 0.8, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <div
              className="absolute top-1/2 left-1/2 w-60 h-60 -translate-x-1/2 -translate-y-1/2 rounded-full"
              style={{
                background: `radial-gradient(circle, 
                  rgba(20, 184, 166, 0.3) 0%, 
                  rgba(20, 184, 166, 0.1) 50%, 
                  transparent 70%)`
              }}
            />
          </motion.div>

          {/* Outer Ring Pulses */}
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={`ring-${i}`}
              className="absolute inset-0 pointer-events-none"
              animate={{
                scale: [0, 2, 3],
                opacity: [0.8, 0.3, 0]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 1,
                ease: "easeOut"
              }}
            >
              <div
                className="absolute top-1/2 left-1/2 w-4 h-4 -translate-x-1/2 -translate-y-1/2 rounded-full border-2"
                style={{
                  borderColor: "rgba(20, 184, 166, 0.5)"
                }}
              />
            </motion.div>
          ))}
        </div>

        {/* Loading Text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="absolute bottom-1/3 left-1/2 -translate-x-1/2"
        >
          <div className="text-center space-y-4">
            <motion.p
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="text-teal-400 text-lg font-medium tracking-wider"
            >
              Loading Portfolio
            </motion.p>
            <div className="flex space-x-1 justify-center">
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-teal-400 rounded-full"
                  animate={{ scale: [1, 1.5, 1], opacity: [0.5, 1, 0.5] }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </motion.div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-b from-gray-950 to-black flex items-center justify-center">
      <div className="relative">
        {/* Lightning Bolt */}
        <motion.div
          initial={{ scale: 0, rotate: -10 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="relative z-10"
        >
          <svg
            width="300"
            height="400"
            viewBox="0 0 300 400"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="drop-shadow-2xl"
          >
            {/* Main Lightning Bolt */}
            <path
              d="M150 20 L180 20 L120 160 L180 160 L80 380 L120 380 L100 240 L160 240 L150 20 Z"
              fill="url(#lightning-gradient)"
              stroke="url(#lightning-border)"
              strokeWidth="2"
            />
            {/* Inner Lightning Glow */}
            <path
              d="M155 25 L175 25 L125 165 L170 165 L90 375 L115 375 L105 245 L155 245 L155 25 Z"
              fill="url(#inner-glow)"
              opacity="0.9"
            />
            {/* Lightning Core */}
            <path
              d="M160 30 L170 30 L130 170 L165 170 L95 370 L110 370 L110 250 L150 250 L160 30 Z"
              fill="rgba(255, 255, 255, 0.8)"
              opacity="0.7"
            />
            <defs>
              <linearGradient id="lightning-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="rgba(255, 255, 255, 0.95)" />
                <stop offset="20%" stopColor="rgba(20, 184, 166, 0.9)" />
                <stop offset="60%" stopColor="rgba(20, 184, 166, 0.7)" />
                <stop offset="100%" stopColor="rgba(20, 184, 166, 0.4)" />
              </linearGradient>
              <linearGradient id="lightning-border" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
                <stop offset="50%" stopColor="rgba(20, 184, 166, 1)" />
                <stop offset="100%" stopColor="rgba(20, 184, 166, 0.6)" />
              </linearGradient>
              <linearGradient id="inner-glow" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="rgba(255, 255, 255, 0.7)" />
                <stop offset="40%" stopColor="rgba(20, 184, 166, 0.6)" />
                <stop offset="100%" stopColor="rgba(20, 184, 166, 0.2)" />
              </linearGradient>
            </defs>
          </svg>
        </motion.div>

        {/* Animated Gradient Rays */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute inset-0 pointer-events-none"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ 
              opacity: [0, 0.8, 0],
              scale: [0, 1.5, 2],
              rotate: i * 45
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeOut"
            }}
          >
            <div
              className="absolute top-1/2 left-1/2 w-3 h-48 -translate-x-1/2 -translate-y-1/2 origin-bottom"
              style={{
                background: `linear-gradient(to top, 
                  rgba(20, 184, 166, 0.6) 0%, 
                  rgba(20, 184, 166, 0.3) 50%, 
                  transparent 100%)`
              }}
            />
          </motion.div>
        ))}

        {/* Central Glow */}
        <motion.div
          className="absolute inset-0 pointer-events-none"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <div
            className="absolute top-1/2 left-1/2 w-60 h-60 -translate-x-1/2 -translate-y-1/2 rounded-full"
            style={{
              background: `radial-gradient(circle, 
                rgba(20, 184, 166, 0.3) 0%, 
                rgba(20, 184, 166, 0.1) 50%, 
                transparent 70%)`
            }}
          />
        </motion.div>

        {/* Outer Ring Pulses */}
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={`ring-${i}`}
            className="absolute inset-0 pointer-events-none"
            animate={{
              scale: [0, 2, 3],
              opacity: [0.8, 0.3, 0]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 1,
              ease: "easeOut"
            }}
          >
            <div
              className="absolute top-1/2 left-1/2 w-4 h-4 -translate-x-1/2 -translate-y-1/2 rounded-full border-2"
              style={{
                borderColor: "rgba(20, 184, 166, 0.5)"
              }}
            />
          </motion.div>
        ))}
      </div>

      {/* Loading Text */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.6 }}
        className="absolute bottom-1/3 left-1/2 -translate-x-1/2"
      >
        <div className="text-center space-y-4">
          <motion.p
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-teal-400 text-lg font-medium tracking-wider"
          >
            Loading Portfolio
          </motion.p>
          <div className="flex space-x-1 justify-center">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-teal-400 rounded-full"
                animate={{ scale: [1, 1.5, 1], opacity: [0.5, 1, 0.5] }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
