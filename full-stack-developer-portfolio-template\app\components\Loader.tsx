"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";

interface LoaderProps {
  onLoadingComplete: () => void;
}

export default function Loader({ onLoadingComplete }: LoaderProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onLoadingComplete, 500); // Wait for fade out animation
    }, 2500); // Show loader for 2.5 seconds

    return () => clearTimeout(timer);
  }, [onLoadingComplete]);

  if (!isVisible) {
    return (
      <motion.div
        initial={{ opacity: 1 }}
        animate={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="fixed inset-0 z-50 bg-gradient-to-b from-gray-950 to-black flex items-center justify-center"
      >
        <div className="relative">
          {/* Blue Flame */}
          <motion.div initial={{ scale: 0, y: 20 }} animate={{ scale: 1, y: 0 }} transition={{ duration: 0.8, ease: "easeOut" }} className="relative z-10">
            <svg width="300" height="400" viewBox="0 0 300 400" fill="none" xmlns="http://www.w3.org/2000/svg" className="drop-shadow-2xl">
              {/* Main Flame Body */}
              <motion.path
                d="M150 380 C120 360, 100 320, 110 280 C105 240, 120 200, 140 170 C130 140, 140 110, 160 90 C170 70, 180 50, 190 30 C200 50, 210 70, 200 90 C220 110, 210 140, 200 170 C220 200, 235 240, 230 280 C240 320, 220 360, 190 380 C170 385, 160 385, 150 380 Z"
                fill="url(#flame-gradient)"
                animate={{
                  d: [
                    "M150 380 C120 360, 100 320, 110 280 C105 240, 120 200, 140 170 C130 140, 140 110, 160 90 C170 70, 180 50, 190 30 C200 50, 210 70, 200 90 C220 110, 210 140, 200 170 C220 200, 235 240, 230 280 C240 320, 220 360, 190 380 C170 385, 160 385, 150 380 Z",
                    "M150 380 C125 365, 105 325, 115 285 C100 245, 125 205, 145 175 C135 145, 145 115, 165 95 C175 75, 185 55, 195 35 C205 55, 215 75, 205 95 C225 115, 215 145, 205 175 C225 205, 240 245, 235 285 C245 325, 225 365, 195 380 C175 385, 165 385, 150 380 Z",
                    "M150 380 C115 355, 95 315, 105 275 C110 235, 115 195, 135 165 C125 135, 135 105, 155 85 C165 65, 175 45, 185 25 C195 45, 205 65, 195 85 C215 105, 205 135, 195 165 C215 195, 230 235, 225 275 C235 315, 215 355, 185 380 C165 385, 155 385, 150 380 Z",
                  ],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />

              {/* Inner Flame */}
              <motion.path
                d="M150 370 C130 355, 115 320, 125 285 C120 250, 135 215, 150 190 C145 165, 150 140, 165 125 C170 110, 175 95, 180 80 C185 95, 190 110, 185 125 C195 140, 190 165, 185 190 C200 215, 210 250, 205 285 C215 320, 200 355, 180 370 C165 375, 160 375, 150 370 Z"
                fill="url(#inner-flame-gradient)"
                animate={{
                  d: [
                    "M150 370 C130 355, 115 320, 125 285 C120 250, 135 215, 150 190 C145 165, 150 140, 165 125 C170 110, 175 95, 180 80 C185 95, 190 110, 185 125 C195 140, 190 165, 185 190 C200 215, 210 250, 205 285 C215 320, 200 355, 180 370 C165 375, 160 375, 150 370 Z",
                    "M150 370 C135 360, 120 325, 130 290 C125 255, 140 220, 155 195 C150 170, 155 145, 170 130 C175 115, 180 100, 185 85 C190 100, 195 115, 190 130 C200 145, 195 170, 190 195 C205 220, 215 255, 210 290 C220 325, 205 360, 185 370 C170 375, 165 375, 150 370 Z",
                    "M150 370 C125 350, 110 315, 120 280 C115 245, 130 210, 145 185 C140 160, 145 135, 160 120 C165 105, 170 90, 175 75 C180 90, 185 105, 180 120 C190 135, 185 160, 180 185 C195 210, 205 245, 200 280 C210 315, 195 350, 175 370 C160 375, 155 375, 150 370 Z",
                  ],
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.3,
                }}
              />

              {/* Flame Core */}
              <motion.path
                d="M150 360 C140 350, 130 325, 140 300 C135 275, 145 250, 155 230 C152 210, 155 190, 165 180 C168 170, 170 160, 172 150 C174 160, 176 170, 174 180 C180 190, 178 210, 175 230 C185 250, 190 275, 185 300 C195 325, 185 350, 175 360 C165 365, 160 365, 150 360 Z"
                fill="url(#core-flame-gradient)"
                animate={{
                  d: [
                    "M150 360 C140 350, 130 325, 140 300 C135 275, 145 250, 155 230 C152 210, 155 190, 165 180 C168 170, 170 160, 172 150 C174 160, 176 170, 174 180 C180 190, 178 210, 175 230 C185 250, 190 275, 185 300 C195 325, 185 350, 175 360 C165 365, 160 365, 150 360 Z",
                    "M150 360 C145 355, 135 330, 145 305 C140 280, 150 255, 160 235 C157 215, 160 195, 170 185 C173 175, 175 165, 177 155 C179 165, 181 175, 179 185 C185 195, 183 215, 180 235 C190 255, 195 280, 190 305 C200 330, 190 355, 180 360 C170 365, 165 365, 150 360 Z",
                  ],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.6,
                }}
              />

              <defs>
                <radialGradient id="flame-gradient" cx="50%" cy="80%" r="60%">
                  <stop offset="0%" stopColor="rgba(0, 191, 255, 0.9)" />
                  <stop offset="30%" stopColor="rgba(30, 144, 255, 0.8)" />
                  <stop offset="60%" stopColor="rgba(65, 105, 225, 0.7)" />
                  <stop offset="100%" stopColor="rgba(25, 25, 112, 0.5)" />
                </radialGradient>
                <radialGradient id="inner-flame-gradient" cx="50%" cy="75%" r="50%">
                  <stop offset="0%" stopColor="rgba(135, 206, 250, 0.9)" />
                  <stop offset="40%" stopColor="rgba(70, 130, 180, 0.8)" />
                  <stop offset="80%" stopColor="rgba(30, 144, 255, 0.6)" />
                  <stop offset="100%" stopColor="rgba(0, 100, 200, 0.4)" />
                </radialGradient>
                <radialGradient id="core-flame-gradient" cx="50%" cy="70%" r="40%">
                  <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
                  <stop offset="30%" stopColor="rgba(173, 216, 230, 0.8)" />
                  <stop offset="70%" stopColor="rgba(135, 206, 250, 0.6)" />
                  <stop offset="100%" stopColor="rgba(100, 149, 237, 0.4)" />
                </radialGradient>
              </defs>
            </svg>
          </motion.div>

          {/* Animated Blue Flame Particles */}
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute inset-0 pointer-events-none"
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: [0, 0.7, 0],
                scale: [0, 1.2, 1.8],
                rotate: i * 30,
                y: [0, -20, -40],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.15,
                ease: "easeOut",
              }}
            >
              <div
                className="absolute top-1/2 left-1/2 w-2 h-32 -translate-x-1/2 -translate-y-1/2 origin-bottom rounded-full"
                style={{
                  background: `linear-gradient(to top,
                    rgba(0, 191, 255, 0.8) 0%,
                    rgba(30, 144, 255, 0.6) 40%,
                    rgba(135, 206, 250, 0.4) 70%,
                    transparent 100%)`,
                  filter: "blur(1px)",
                }}
              />
            </motion.div>
          ))}

          {/* Central Blue Flame Glow */}
          <motion.div
            className="absolute inset-0 pointer-events-none"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <div
              className="absolute top-1/2 left-1/2 w-80 h-80 -translate-x-1/2 -translate-y-1/2 rounded-full"
              style={{
                background: `radial-gradient(circle,
                  rgba(0, 191, 255, 0.4) 0%,
                  rgba(30, 144, 255, 0.2) 40%,
                  rgba(65, 105, 225, 0.1) 60%,
                  transparent 80%)`,
                filter: "blur(20px)",
              }}
            />
          </motion.div>

          {/* Blue Flame Ring Pulses */}
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={`ring-${i}`}
              className="absolute inset-0 pointer-events-none"
              animate={{
                scale: [0, 2.5, 4],
                opacity: [0.9, 0.4, 0],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: i * 0.8,
                ease: "easeOut",
              }}
            >
              <div
                className="absolute top-1/2 left-1/2 w-6 h-6 -translate-x-1/2 -translate-y-1/2 rounded-full border-2"
                style={{
                  borderColor: "rgba(0, 191, 255, 0.6)",
                  boxShadow: "0 0 20px rgba(0, 191, 255, 0.3)",
                }}
              />
            </motion.div>
          ))}
        </div>

        {/* Loading Text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="absolute bottom-1/3 left-1/2 -translate-x-1/2"
        >
          <div className="text-center space-y-4">
            <motion.p animate={{ opacity: [0.5, 1, 0.5] }} transition={{ duration: 2, repeat: Infinity }} className="text-blue-400 text-lg font-medium tracking-wider">
              Loading Portfolio
            </motion.p>
            <div className="flex space-x-1 justify-center">
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-blue-400 rounded-full"
                  animate={{ scale: [1, 1.5, 1], opacity: [0.5, 1, 0.5] }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </motion.div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-b from-gray-950 to-black flex items-center justify-center">
      <div className="relative">
        {/* Blue Flame */}
        <motion.div initial={{ scale: 0, y: 20 }} animate={{ scale: 1, y: 0 }} transition={{ duration: 0.8, ease: "easeOut" }} className="relative z-10">
          <svg width="300" height="400" viewBox="0 0 300 400" fill="none" xmlns="http://www.w3.org/2000/svg" className="drop-shadow-2xl">
            {/* Main Flame Body */}
            <motion.path
              d="M150 380 C120 360, 100 320, 110 280 C105 240, 120 200, 140 170 C130 140, 140 110, 160 90 C170 70, 180 50, 190 30 C200 50, 210 70, 200 90 C220 110, 210 140, 200 170 C220 200, 235 240, 230 280 C240 320, 220 360, 190 380 C170 385, 160 385, 150 380 Z"
              fill="url(#flame-gradient-main)"
              animate={{
                d: [
                  "M150 380 C120 360, 100 320, 110 280 C105 240, 120 200, 140 170 C130 140, 140 110, 160 90 C170 70, 180 50, 190 30 C200 50, 210 70, 200 90 C220 110, 210 140, 200 170 C220 200, 235 240, 230 280 C240 320, 220 360, 190 380 C170 385, 160 385, 150 380 Z",
                  "M150 380 C125 365, 105 325, 115 285 C100 245, 125 205, 145 175 C135 145, 145 115, 165 95 C175 75, 185 55, 195 35 C205 55, 215 75, 205 95 C225 115, 215 145, 205 175 C225 205, 240 245, 235 285 C245 325, 225 365, 195 380 C175 385, 165 385, 150 380 Z",
                  "M150 380 C115 355, 95 315, 105 275 C110 235, 115 195, 135 165 C125 135, 135 105, 155 85 C165 65, 175 45, 185 25 C195 45, 205 65, 195 85 C215 105, 205 135, 195 165 C215 195, 230 235, 225 275 C235 315, 215 355, 185 380 C165 385, 155 385, 150 380 Z",
                ],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />

            {/* Inner Flame */}
            <motion.path
              d="M150 370 C130 355, 115 320, 125 285 C120 250, 135 215, 150 190 C145 165, 150 140, 165 125 C170 110, 175 95, 180 80 C185 95, 190 110, 185 125 C195 140, 190 165, 185 190 C200 215, 210 250, 205 285 C215 320, 200 355, 180 370 C165 375, 160 375, 150 370 Z"
              fill="url(#inner-flame-gradient-main)"
              animate={{
                d: [
                  "M150 370 C130 355, 115 320, 125 285 C120 250, 135 215, 150 190 C145 165, 150 140, 165 125 C170 110, 175 95, 180 80 C185 95, 190 110, 185 125 C195 140, 190 165, 185 190 C200 215, 210 250, 205 285 C215 320, 200 355, 180 370 C165 375, 160 375, 150 370 Z",
                  "M150 370 C135 360, 120 325, 130 290 C125 255, 140 220, 155 195 C150 170, 155 145, 170 130 C175 115, 180 100, 185 85 C190 100, 195 115, 190 130 C200 145, 195 170, 190 195 C205 220, 215 255, 210 290 C220 325, 205 360, 185 370 C170 375, 165 375, 150 370 Z",
                  "M150 370 C125 350, 110 315, 120 280 C115 245, 130 210, 145 185 C140 160, 145 135, 160 120 C165 105, 170 90, 175 75 C180 90, 185 105, 180 120 C190 135, 185 160, 180 185 C195 210, 205 245, 200 280 C210 315, 195 350, 175 370 C160 375, 155 375, 150 370 Z",
                ],
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 0.3,
              }}
            />

            {/* Flame Core */}
            <motion.path
              d="M150 360 C140 350, 130 325, 140 300 C135 275, 145 250, 155 230 C152 210, 155 190, 165 180 C168 170, 170 160, 172 150 C174 160, 176 170, 174 180 C180 190, 178 210, 175 230 C185 250, 190 275, 185 300 C195 325, 185 350, 175 360 C165 365, 160 365, 150 360 Z"
              fill="url(#core-flame-gradient-main)"
              animate={{
                d: [
                  "M150 360 C140 350, 130 325, 140 300 C135 275, 145 250, 155 230 C152 210, 155 190, 165 180 C168 170, 170 160, 172 150 C174 160, 176 170, 174 180 C180 190, 178 210, 175 230 C185 250, 190 275, 185 300 C195 325, 185 350, 175 360 C165 365, 160 365, 150 360 Z",
                  "M150 360 C145 355, 135 330, 145 305 C140 280, 150 255, 160 235 C157 215, 160 195, 170 185 C173 175, 175 165, 177 155 C179 165, 181 175, 179 185 C185 195, 183 215, 180 235 C190 255, 195 280, 190 305 C200 330, 190 355, 180 360 C170 365, 165 365, 150 360 Z",
                ],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 0.6,
              }}
            />

            <defs>
              <radialGradient id="flame-gradient-main" cx="50%" cy="80%" r="60%">
                <stop offset="0%" stopColor="rgba(0, 191, 255, 0.9)" />
                <stop offset="30%" stopColor="rgba(30, 144, 255, 0.8)" />
                <stop offset="60%" stopColor="rgba(65, 105, 225, 0.7)" />
                <stop offset="100%" stopColor="rgba(25, 25, 112, 0.5)" />
              </radialGradient>
              <radialGradient id="inner-flame-gradient-main" cx="50%" cy="75%" r="50%">
                <stop offset="0%" stopColor="rgba(135, 206, 250, 0.9)" />
                <stop offset="40%" stopColor="rgba(70, 130, 180, 0.8)" />
                <stop offset="80%" stopColor="rgba(30, 144, 255, 0.6)" />
                <stop offset="100%" stopColor="rgba(0, 100, 200, 0.4)" />
              </radialGradient>
              <radialGradient id="core-flame-gradient-main" cx="50%" cy="70%" r="40%">
                <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
                <stop offset="30%" stopColor="rgba(173, 216, 230, 0.8)" />
                <stop offset="70%" stopColor="rgba(135, 206, 250, 0.6)" />
                <stop offset="100%" stopColor="rgba(100, 149, 237, 0.4)" />
              </radialGradient>
            </defs>
          </svg>
        </motion.div>

        {/* Animated Blue Flame Particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute inset-0 pointer-events-none"
            initial={{ opacity: 0, scale: 0 }}
            animate={{
              opacity: [0, 0.7, 0],
              scale: [0, 1.2, 1.8],
              rotate: i * 30,
              y: [0, -20, -40],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.15,
              ease: "easeOut",
            }}
          >
            <div
              className="absolute top-1/2 left-1/2 w-2 h-32 -translate-x-1/2 -translate-y-1/2 origin-bottom rounded-full"
              style={{
                background: `linear-gradient(to top,
                  rgba(0, 191, 255, 0.8) 0%,
                  rgba(30, 144, 255, 0.6) 40%,
                  rgba(135, 206, 250, 0.4) 70%,
                  transparent 100%)`,
                filter: "blur(1px)",
              }}
            />
          </motion.div>
        ))}

        {/* Central Blue Flame Glow */}
        <motion.div
          className="absolute inset-0 pointer-events-none"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 2.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          <div
            className="absolute top-1/2 left-1/2 w-80 h-80 -translate-x-1/2 -translate-y-1/2 rounded-full"
            style={{
              background: `radial-gradient(circle,
                rgba(0, 191, 255, 0.4) 0%,
                rgba(30, 144, 255, 0.2) 40%,
                rgba(65, 105, 225, 0.1) 60%,
                transparent 80%)`,
              filter: "blur(20px)",
            }}
          />
        </motion.div>

        {/* Blue Flame Ring Pulses */}
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={`ring-${i}`}
            className="absolute inset-0 pointer-events-none"
            animate={{
              scale: [0, 2.5, 4],
              opacity: [0.9, 0.4, 0],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              delay: i * 0.8,
              ease: "easeOut",
            }}
          >
            <div
              className="absolute top-1/2 left-1/2 w-6 h-6 -translate-x-1/2 -translate-y-1/2 rounded-full border-2"
              style={{
                borderColor: "rgba(0, 191, 255, 0.6)",
                boxShadow: "0 0 20px rgba(0, 191, 255, 0.3)",
              }}
            />
          </motion.div>
        ))}
      </div>

      {/* Loading Text */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.6 }}
        className="absolute bottom-1/3 left-1/2 -translate-x-1/2"
      >
        <div className="text-center space-y-4">
          <motion.p animate={{ opacity: [0.5, 1, 0.5] }} transition={{ duration: 2, repeat: Infinity }} className="text-blue-400 text-lg font-medium tracking-wider">
            Loading Portfolio
          </motion.p>
          <div className="flex space-x-1 justify-center">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-blue-400 rounded-full"
                animate={{ scale: [1, 1.5, 1], opacity: [0.5, 1, 0.5] }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
