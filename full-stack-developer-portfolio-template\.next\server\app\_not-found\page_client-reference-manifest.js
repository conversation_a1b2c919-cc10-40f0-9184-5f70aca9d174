globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"2328":{"*":{"id":"7174","name":"*","chunks":[],"async":false}},"3866":{"*":{"id":"7190","name":"*","chunks":[],"async":false}},"4547":{"*":{"id":"3875","name":"*","chunks":[],"async":false}},"4835":{"*":{"id":"8903","name":"*","chunks":[],"async":false}},"5244":{"*":{"id":"4178","name":"*","chunks":[],"async":false}},"6213":{"*":{"id":"1365","name":"*","chunks":[],"async":false}},"7033":{"*":{"id":"6959","name":"*","chunks":[],"async":false}},"8930":{"*":{"id":"9096","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":7033,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":7033,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4547,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4547,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":4835,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":4835,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":2328,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":2328,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":5244,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":5244,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":3866,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":3866,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":6213,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":6213,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":2353,"name":"*","chunks":["177","static/chunks/app/layout-1368eddf9d84192b.js"],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7275,"name":"*","chunks":["177","static/chunks/app/layout-1368eddf9d84192b.js"],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\globals.css":{"id":9324,"name":"*","chunks":["177","static/chunks/app/layout-1368eddf9d84192b.js"],"async":false},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\page.tsx":{"id":8930,"name":"*","chunks":["704","static/chunks/704-c1b7454acf2285d3.js","974","static/chunks/app/page-a4cc83484493c8d6.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\":[],"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\layout":[{"inlined":false,"path":"static/css/499f96a3048ebeb3.css"}],"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\page":[],"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\_not-found\\page":[]},"rscModuleMapping":{"2328":{"*":{"id":"802","name":"*","chunks":[],"async":false}},"3866":{"*":{"id":"8530","name":"*","chunks":[],"async":false}},"4547":{"*":{"id":"4863","name":"*","chunks":[],"async":false}},"4835":{"*":{"id":"5155","name":"*","chunks":[],"async":false}},"5244":{"*":{"id":"9350","name":"*","chunks":[],"async":false}},"6213":{"*":{"id":"8921","name":"*","chunks":[],"async":false}},"7033":{"*":{"id":"3219","name":"*","chunks":[],"async":false}},"8930":{"*":{"id":"5104","name":"*","chunks":[],"async":false}},"9324":{"*":{"id":"2704","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}