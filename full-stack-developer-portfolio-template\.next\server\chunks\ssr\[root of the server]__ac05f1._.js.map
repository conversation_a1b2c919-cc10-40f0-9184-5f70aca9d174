{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Portfolio/devportfoliotemplates/full-stack-developer-portfolio-template/app/components/HeroSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { useState, useEffect } from \"react\";\r\n\r\nexport default function HeroSection() {\r\n  const [selectedStack, setSelectedStack] = useState<\"frontend\" | \"backend\" | \"devops\" | null>(null);\r\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\r\n\r\n  useEffect(() => {\r\n    const handleMouseMove = (e: MouseEvent) => {\r\n      setMousePosition({ x: e.clientX, y: e.clientY });\r\n    };\r\n\r\n    window.addEventListener(\"mousemove\", handleMouseMove);\r\n    return () => window.removeEventListener(\"mousemove\", handleMouseMove);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"min-h-screen relative overflow-hidden py-24 md:py-0\">\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute inset-0 bg-[url('/grid.svg')] opacity-10\" />\r\n        {/* Mouse-following gradient */}\r\n        <div\r\n          className=\"absolute inset-0 pointer-events-none transition-opacity duration-300\"\r\n          style={{\r\n            background: `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px,\r\n              rgba(20, 184, 166, 0.25) 0%,\r\n              rgba(20, 184, 166, 0.15) 25%,\r\n              rgba(20, 184, 166, 0.08) 50%,\r\n              transparent 70%)`,\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"relative z-10 min-h-screen flex flex-col items-center justify-center px-4 pt-8 md:pt-0\">\r\n        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className=\"text-center mb-8 md:mb-12\">\r\n          <div className=\"space-y-3 md:space-y-4 mb-6 md:mb-8\">\r\n            <h1 className=\"text-4xl md:text-7xl font-bold text-transparent bg-clip-text text-white\">Ben Basil Tomy</h1>\r\n            <h2 className=\"text-2xl md:text-4xl font-bold text-gray-300\">Software Engineer</h2>\r\n            <p className=\"text-lg md:text-xl text-gray-400 max-w-2xl mx-auto\">I craft end-to-end solutions with 2+ years of experience building scalable applications.</p>\r\n            {/* <p className=\"text-base md:text-lg text-gray-500 max-w-2xl mx-auto\">\r\n              Currently Software Engineer at <span className=\"text-blue-400\">Cavli Wireless</span>\r\n            </p> */}\r\n            {/* <p className=\"text-base md:text-lg text-gray-500 max-w-2xl mx-auto\">\r\n\t\t\t\t\t\t\tCurrently Software Engineer at <span className=\"text-blue-400\">Cavli Wireless</span> and Tech Lead at{' '}\r\n\t\t\t\t\t\t\t<span className=\"text-purple-400\">Vercel</span>\r\n\t\t\t\t\t\t</p> */}\r\n          </div>\r\n          {/* <div className=\"flex flex-wrap justify-center gap-2 md:gap-3 mb-6 md:mb-8\">\r\n            <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-blue-400 text-xs md:text-sm\">Javascript</span>\r\n            <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-purple-400 text-xs md:text-sm\">Express.js</span>\r\n            <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-teal-500/10 rounded-full text-teal-400 text-xs md:text-sm\">Node.js</span>\r\n            <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-blue-400 text-xs md:text-sm\">Mongo DB</span>\r\n            <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-purple-400 text-xs md:text-sm\">AWS</span>\r\n            <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-blue-400 text-xs md:text-sm\">TypeScript</span>\r\n          </div> */}\r\n        </motion.div>\r\n\r\n        {/* Interactive System Architecture */}\r\n        <div className=\"w-full max-w-5xl mx-auto relative px-2 md:px-4\">\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ delay: 0.3 }}\r\n            className=\"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800 p-4 md:p-8\"\r\n          >\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8\">\r\n              {/* Frontend Layer */}\r\n              {/* <div\r\n                className={`p-4 md:p-6 rounded-lg transition-colors border-2 ${\r\n                  selectedStack === \"frontend\" ? \"bg-blue-500/20 border-blue-500/50\" : \"bg-gray-800/50 hover:bg-gray-800/80 border-transparent\"\r\n                }`}\r\n                onMouseEnter={() => setSelectedStack(\"frontend\")}\r\n                onMouseLeave={() => setSelectedStack(null)}\r\n              >\r\n                <h3 className=\"text-lg md:text-xl font-semibold mb-3 md:mb-4 text-blue-400\">Frontend Development</h3>\r\n                <ul className=\"space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400\">\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\" />\r\n                    Modern Angular Architectures\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\" />\r\n                    Performance Optimization\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\" />\r\n                    Responsive & Interactive UIs\r\n                  </li>\r\n                </ul>\r\n              </div> */}\r\n\r\n              {/* Backend Layer */}\r\n              {/* <div\r\n                className={`p-4 md:p-6 rounded-lg transition-colors border-2 ${\r\n                  selectedStack === \"backend\" ? \"bg-purple-500/20 border-purple-500/50\" : \"bg-gray-800/50 hover:bg-gray-800/80 border-transparent\"\r\n                }`}\r\n                onMouseEnter={() => setSelectedStack(\"backend\")}\r\n                onMouseLeave={() => setSelectedStack(null)}\r\n              >\r\n                <h3 className=\"text-lg md:text-xl font-semibold mb-3 md:mb-4 text-purple-400\">Backend Development</h3>\r\n                <ul className=\"space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400\">\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\" />\r\n                    API Design & Development\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\" />\r\n                    Database Architecture\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\" />\r\n                    Real-time Systems\r\n                  </li>\r\n                </ul>\r\n              </div> */}\r\n\r\n              {/* Frontend */}\r\n              <div\r\n                className={`p-4 md:p-6 rounded-lg transition-colors border-2 ${\r\n                  selectedStack === \"frontend\" ? \"bg-teal-500/20 border-teal-500/50\" : \"bg-gray-800/50 hover:bg-gray-800/80 border-transparent\"\r\n                }`}\r\n                onMouseEnter={() => setSelectedStack(\"frontend\")}\r\n                onMouseLeave={() => setSelectedStack(null)}\r\n              >\r\n                <h3 className=\"text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400\">Frontend Development</h3>\r\n                <ul className=\"space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400\">\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    Modern Angular Architectures\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    Performance Optimization\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    Performance Optimization\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n\r\n              {/* Backend */}\r\n              <div\r\n                className={`p-4 md:p-6 rounded-lg transition-colors border-2 ${\r\n                  selectedStack === \"backend\" ? \"bg-teal-500/20 border-teal-500/50\" : \"bg-gray-800/50 hover:bg-gray-800/80 border-transparent\"\r\n                }`}\r\n                onMouseEnter={() => setSelectedStack(\"backend\")}\r\n                onMouseLeave={() => setSelectedStack(null)}\r\n              >\r\n                <h3 className=\"text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400\">Backend Development</h3>\r\n                <ul className=\"space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400\">\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    API Design & Development\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    Database Architecture\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    Real-time Systems\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n\r\n              {/* DevOps Layer */}\r\n              <div\r\n                className={`p-4 md:p-6 rounded-lg transition-colors border-2 ${\r\n                  selectedStack === \"devops\" ? \"bg-teal-500/20 border-teal-500/50\" : \"bg-gray-800/50 hover:bg-gray-800/80 border-transparent\"\r\n                }`}\r\n                onMouseEnter={() => setSelectedStack(\"devops\")}\r\n                onMouseLeave={() => setSelectedStack(null)}\r\n              >\r\n                <h3 className=\"text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400\">DevOps & Cloud</h3>\r\n                <ul className=\"space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400\">\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    AWS Infrastructure\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    Docker\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-1.5 h-1.5 bg-teal-500 rounded-full\" />\r\n                    Scalable Architecture\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AADA;AAFA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAC7F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;IACvD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,gCAAgC,EAAE,cAAc,CAAC,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;;;;8BAIlE,CAAC;wBACrB;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAAG,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAAG,WAAU;kCACnF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;;;;;;;;;;;;kCAoBtE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAoDb,8OAAC;wCACC,WAAW,CAAC,iDAAiD,EAC3D,kBAAkB,aAAa,sCAAsC,0DACrE;wCACF,cAAc,IAAM,iBAAiB;wCACrC,cAAc,IAAM,iBAAiB;;0DAErC,8OAAC;gDAAG,WAAU;0DAA8D;;;;;;0DAC5E,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;kEAG1D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;kEAG1D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;;;;;;;;;;;;;kDAO9D,8OAAC;wCACC,WAAW,CAAC,iDAAiD,EAC3D,kBAAkB,YAAY,sCAAsC,0DACpE;wCACF,cAAc,IAAM,iBAAiB;wCACrC,cAAc,IAAM,iBAAiB;;0DAErC,8OAAC;gDAAG,WAAU;0DAA8D;;;;;;0DAC5E,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;kEAG1D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;kEAG1D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;;;;;;;;;;;;;kDAO9D,8OAAC;wCACC,WAAW,CAAC,iDAAiD,EAC3D,kBAAkB,WAAW,sCAAsC,0DACnE;wCACF,cAAc,IAAM,iBAAiB;wCACrC,cAAc,IAAM,iBAAiB;;0DAErC,8OAAC;gDAAG,WAAU;0DAA8D;;;;;;0DAC5E,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;kEAG1D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;kEAG1D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5E"}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Portfolio/devportfoliotemplates/full-stack-developer-portfolio-template/app/components/ContactSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\n\r\nexport default function ContactSection() {\r\n  return (\r\n    <section className=\"py-20 px-4\">\r\n      <div className=\"max-w-4xl mx-auto text-center\">\r\n        <motion.div initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }} className=\"space-y-8\">\r\n          <h2 className=\"text-3xl font-bold\">Let&apos;s Build Something Amazing</h2>\r\n          <p className=\"text-gray-400 max-w-2xl mx-auto\">Looking for a software engineer who can architect and implement complete solutions? Let&apos;s discuss your project.</p>\r\n\r\n          {/* Location */}\r\n          <div className=\"flex items-center justify-center gap-2 text-gray-400\">\r\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n            </svg>\r\n            <span>India</span>\r\n          </div>\r\n\r\n          {/* Contact Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row justify-center gap-4\">\r\n            <a\r\n              href=\"mailto:<EMAIL>\"\r\n              className=\"px-8 py-3 bg-teal-500 rounded-lg font-medium hover:opacity-90 transition-opacity inline-flex items-center justify-center gap-2\"\r\n            >\r\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\" />\r\n                <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\" />\r\n              </svg>\r\n              Get in Touch\r\n            </a>\r\n            <a\r\n              href=\"/resume.pdf\"\r\n              target=\"_blank\"\r\n              className=\"px-8 py-3 bg-black border border-gray-800 rounded-lg font-medium hover:bg-gray-900 transition-colors inline-flex items-center justify-center gap-2\"\r\n            >\r\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z\" clipRule=\"evenodd\" />\r\n                <path fillRule=\"evenodd\" d=\"M8 11a1 1 0 100 2h4a1 1 0 100-2H8zm0-4a1 1 0 100 2h4a1 1 0 100-2H8z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n              View Resume\r\n            </a>\r\n          </div>\r\n\r\n          {/* Social Links */}\r\n          <div className=\"flex justify-center gap-6\">\r\n            <a\r\n              href=\"https://github.com/BenBasilTomy\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg\"\r\n            >\r\n              <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\" />\r\n              </svg>\r\n            </a>\r\n            <a\r\n              href=\"https://www.linkedin.com/in/ben-basil-tomy-5533b9218/\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg\"\r\n            >\r\n              <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path d=\"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\" />\r\n              </svg>\r\n            </a>\r\n            {/* <a href=\"https://twitter.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg\">\r\n              <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\" />\r\n              </svg>\r\n            </a> */}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAAG,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAAG,UAAU;oBAAE,MAAM;gBAAK;gBAAG,WAAU;;kCACjH,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;kCAAkC;;;;;;kCAG/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;;kDACjE,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;kDACrE,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;0CAEvE,8OAAC;0CAAK;;;;;;;;;;;;kCAIR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;;0DACnD,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;;;;;;;oCACJ;;;;;;;0CAGR,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;;0DACnD,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAsG,UAAS;;;;;;0DAC1I,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAsE,UAAS;;;;;;;;;;;;oCACtG;;;;;;;;;;;;;kCAMV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaxB"}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Portfolio/devportfoliotemplates/full-stack-developer-portfolio-template/app/components/ExperienceSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, useScroll, useTransform } from \"framer-motion\";\r\nimport Image from \"next/image\";\r\nimport { useState, useRef } from \"react\";\r\n\r\nexport default function ExperienceSection() {\r\n  const [selectedExp, setSelectedExp] = useState<\"job\" | \"backend\" | \"devops\" | null>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n\r\n  const { scrollYProgress } = useScroll({\r\n    target: containerRef,\r\n    offset: [\"start end\", \"end start\"],\r\n  });\r\n\r\n  const rotateX = useTransform(scrollYProgress, [0, 0.5, 1], [15, 0, -5]);\r\n\r\n  return (\r\n    <section className=\"py-20 px-4\">\r\n      <div className=\"max-w-6xl mx-auto\">\r\n        <motion.h2 initial={{ opacity: 0 }} whileInView={{ opacity: 1 }} viewport={{ once: true }} className=\"text-3xl font-bold mb-16 text-center\">\r\n          Experience\r\n        </motion.h2>\r\n\r\n        <div className=\"space-y-16\">\r\n          {/* E-commerce Platform */}\r\n          <motion.div\r\n            ref={containerRef}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            viewport={{ once: true }}\r\n            style={{\r\n              rotateX,\r\n              transformPerspective: 1000,\r\n              transformStyle: \"preserve-3d\",\r\n            }}\r\n            className=\"bg-gray-900/50 rounded-xl overflow-hidden border border-gray-800\"\r\n          >\r\n            <div\r\n              className={`p-4 md:p-6 rounded-lg transition-colors ${\r\n                selectedExp === \"job\" ? \"bg-teal-500/20 border-teal-500/50\" : \"bg-gray-800/50 hover:bg-gray-800/80 border-transparent\"\r\n              }`}\r\n              onMouseEnter={() => setSelectedExp(\"job\")}\r\n              onMouseLeave={() => setSelectedExp(null)}\r\n            >\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n                  <div className=\"space-y-6\">\r\n                    <div>\r\n                      <h3 className=\"text-2xl font-bold mb-4\">Cavli Wireless</h3>\r\n                      <p className=\"text-gray-400\">\r\n                        A high-performance e-commerce solution handling 100K+ daily transactions with real-time inventory and ML-powered recommendations.\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"grid grid-cols-2 gap-6\">\r\n                      <div>\r\n                        <h4 className=\"text-sm font-semibold text-teal-400 mb-3\">Frontend Architecture</h4>\r\n                        <ul className=\"space-y-2 text-sm text-gray-400\">\r\n                          <li>• Next.js Server Components</li>\r\n                          <li>• Real-time Cart & Inventory</li>\r\n                          <li>• Stripe Payment Integration</li>\r\n                          <li>• PWA with Offline Support</li>\r\n                        </ul>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-semibold text-teal-400 mb-3\">Backend Systems</h4>\r\n                        <ul className=\"space-y-2 text-sm text-gray-400\">\r\n                          <li>• Node.js Microservices</li>\r\n                          <li>• Redis Caching Layer</li>\r\n                          <li>• Kafka Event Streaming</li>\r\n                          <li>• Elasticsearch Product Search</li>\r\n                        </ul>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-3\">\r\n                      <h4 className=\"text-sm font-semibold text-teal-400\">Key Achievements</h4>\r\n                      <ul className=\"space-y-2 text-sm text-gray-400\">\r\n                        <li>• 99.99% Uptime with Blue-Green Deployment</li>\r\n                        <li>• 300ms Average API Response Time</li>\r\n                        <li>• 45% Reduction in Infrastructure Costs</li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"bg-black/30 rounded-xl p-6\">\r\n                    <Image\r\n                      src=\"/hubble-image.webp\" // Path relative to the public directory\r\n                      alt=\"A description of the image\"\r\n                      width={800} // Required for local images to prevent layout shift\r\n                      height={300} // Required for local images to prevent layout shift\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex flex-wrap justify-center gap-2 md:gap-3 mb-6 md:mb-8\">\r\n                <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm\">Javascript</span>\r\n                <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm\">Express.js</span>\r\n                <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-teal-500/10 rounded-full text-teal-400 text-xs md:text-sm\">Node.js</span>\r\n                <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm\">Mongo DB</span>\r\n                <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm\">AWS</span>\r\n                <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm\">TypeScript</span>\r\n                <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm\">NATS</span>\r\n                <span className=\"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm\">Docker</span>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAFA;AAAA;AAAA;AAFA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuC;IACpF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;KAAE,EAAE;QAAC;QAAI;QAAG,CAAC;KAAE;IAEtE,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBAAC,SAAS;wBAAE,SAAS;oBAAE;oBAAG,aAAa;wBAAE,SAAS;oBAAE;oBAAG,UAAU;wBAAE,MAAM;oBAAK;oBAAG,WAAU;8BAAuC;;;;;;8BAI5I,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,OAAO;4BACL;4BACA,sBAAsB;4BACtB,gBAAgB;wBAClB;wBACA,WAAU;kCAEV,cAAA,8OAAC;4BACC,WAAW,CAAC,wCAAwC,EAClD,gBAAgB,QAAQ,sCAAsC,0DAC9D;4BACF,cAAc,IAAM,eAAe;4BACnC,cAAc,IAAM,eAAe;;8CAEnC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA0B;;;;;;0EACxC,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;kEAK/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA2C;;;;;;kFACzD,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;;;;;;;;;;;;;0EAGR,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA2C;;;;;;kFACzD,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;;;;;;;;;;;;;;;;;;;kEAKV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;0DAKV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI,qBAAqB,wCAAwC;;oDACjE,KAAI;oDACJ,OAAO;oDACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA2F;;;;;;sDAC3G,8OAAC;4CAAK,WAAU;sDAA6F;;;;;;sDAC7G,8OAAC;4CAAK,WAAU;sDAA2F;;;;;;sDAC3G,8OAAC;4CAAK,WAAU;sDAA2F;;;;;;sDAC3G,8OAAC;4CAAK,WAAU;sDAA6F;;;;;;sDAC7G,8OAAC;4CAAK,WAAU;sDAA2F;;;;;;sDAC3G,8OAAC;4CAAK,WAAU;sDAA2F;;;;;;sDAC3G,8OAAC;4CAAK,WAAU;sDAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3H"}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Portfolio/devportfoliotemplates/full-stack-developer-portfolio-template/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport HeroS<PERSON><PERSON> from \"./components/HeroSection\";\r\nimport ProjectsSection from \"./components/ProjectsSection\";\r\nimport ContactSection from \"./components/ContactSection\";\r\nimport ExperienceSection from \"./components/ExperienceSection\";\r\nimport Loader from \"./components/Loader\";\r\n\r\nexport default function FullStackPortfolio() {\r\n  return (\r\n    <main className=\"min-h-screen bg-gradient-to-b from-gray-950 to-black text-white\">\r\n      <HeroSection />\r\n      <ExperienceSection />\r\n      {/* <ProjectsSection /> */}\r\n      <ContactSection />\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AANA;;;;;AASe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,iIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,uIAAA,CAAA,UAAiB;;;;;0BAElB,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;;AAGrB"}}, {"offset": {"line": 1129, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}