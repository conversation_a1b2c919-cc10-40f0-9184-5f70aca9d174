"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";
import { useState, useRef } from "react";

export default function ExperienceSection() {
  const [selectedExp, setSelectedExp] = useState<"job" | "backend" | "devops" | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  });

  const rotateX = useTransform(scrollYProgress, [0, 0.5, 1], [15, 0, -5]);

  return (
    <section className="py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <motion.h2 initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }} className="text-3xl font-bold mb-16 text-center">
          Experience
        </motion.h2>

        <div className="space-y-16">
          {/* E-commerce Platform */}
          <motion.div
            ref={containerRef}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            style={{
              rotateX,
              transformPerspective: 1000,
              transformStyle: "preserve-3d",
            }}
            className="bg-gray-900/50 rounded-xl overflow-hidden border border-gray-800"
          >
            <div
              className={`p-4 md:p-6 rounded-lg transition-colors ${
                selectedExp === "job" ? "bg-teal-500/20 border-teal-500/50" : "bg-gray-800/50 hover:bg-gray-800/80 border-transparent"
              }`}
              onMouseEnter={() => setSelectedExp("job")}
              onMouseLeave={() => setSelectedExp(null)}
            >
              <div className="p-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-2xl font-bold mb-4">Cavli Wireless</h3>
                      <p className="text-gray-400">
                        A high-performance e-commerce solution handling 100K+ daily transactions with real-time inventory and ML-powered recommendations.
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-semibold text-teal-400 mb-3">Frontend Architecture</h4>
                        <ul className="space-y-2 text-sm text-gray-400">
                          <li>• Next.js Server Components</li>
                          <li>• Real-time Cart & Inventory</li>
                          <li>• Stripe Payment Integration</li>
                          <li>• PWA with Offline Support</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-teal-400 mb-3">Backend Systems</h4>
                        <ul className="space-y-2 text-sm text-gray-400">
                          <li>• Node.js Microservices</li>
                          <li>• Redis Caching Layer</li>
                          <li>• Kafka Event Streaming</li>
                          <li>• Elasticsearch Product Search</li>
                        </ul>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="text-sm font-semibold text-teal-400">Key Achievements</h4>
                      <ul className="space-y-2 text-sm text-gray-400">
                        <li>• 99.99% Uptime with Blue-Green Deployment</li>
                        <li>• 300ms Average API Response Time</li>
                        <li>• 45% Reduction in Infrastructure Costs</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-black/30 rounded-xl p-6">
                    <Image
                      src="/hubble-image.webp" // Path relative to the public directory
                      alt="A description of the image"
                      width={800} // Required for local images to prevent layout shift
                      height={300} // Required for local images to prevent layout shift
                    />
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap justify-center gap-2 md:gap-3 mb-6 md:mb-8">
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Javascript</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm">Express.js</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-teal-500/10 rounded-full text-teal-400 text-xs md:text-sm">Node.js</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Mongo DB</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm">AWS</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">TypeScript</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">NATS</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Docker</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
