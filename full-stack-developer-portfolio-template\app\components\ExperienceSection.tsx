"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";
import { useState, useRef } from "react";

export default function ExperienceSection() {
  const [selectedExp, setSelectedExp] = useState<"job" | "backend" | "devops" | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  });

  const rotateX = useTransform(scrollYProgress, [0, 0.5, 1], [15, 0, -5]);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      setMousePosition({ x, y });
    }
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
    setSelectedExp("job");
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setSelectedExp(null);
  };

  return (
    <section className="py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <motion.h2 initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }} className="text-3xl font-bold mb-16 text-center">
          Experience
        </motion.h2>

        <div className="space-y-16">
          {/* E-commerce Platform */}
          <motion.div
            ref={containerRef}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            style={{
              rotateX,
              transformPerspective: 1000,
              transformStyle: "preserve-3d",
            }}
            className="relative bg-gray-900/50 rounded-xl overflow-hidden border border-gray-800"
          >
            <div
              ref={cardRef}
              className="relative p-4 md:p-6 rounded-lg transition-all duration-300 bg-gray-800/50 hover:bg-gray-800/60"
              onMouseMove={handleMouseMove}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              style={{
                background: isHovered ? `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(20, 184, 166, 0.15), transparent 40%)` : undefined,
              }}
            >
              {/* Mouse-following border gradient overlay */}
              {isHovered && (
                <div
                  className="absolute inset-0 rounded-lg opacity-60 pointer-events-none"
                  style={{
                    background: `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(20, 184, 166, 0.4), transparent 40%)`,
                    mask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                    maskComposite: "xor",
                    WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                    WebkitMaskComposite: "xor",
                    padding: "1px",
                  }}
                />
              )}

              {/* Additional subtle glow effect */}
              {isHovered && (
                <div
                  className="absolute inset-0 rounded-lg opacity-20 pointer-events-none"
                  style={{
                    background: `radial-gradient(400px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(20, 184, 166, 0.6), transparent 60%)`,
                    filter: "blur(20px)",
                  }}
                />
              )}

              <div className="relative z-10 p-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-2xl font-bold mb-4">Cavli Wireless</h3>
                      <p className="text-gray-400">
                        A high-performance e-commerce solution handling 100K+ daily transactions with real-time inventory and ML-powered recommendations.
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-semibold text-teal-400 mb-3">Frontend Architecture</h4>
                        <ul className="space-y-2 text-sm text-gray-400">
                          <li>• Next.js Server Components</li>
                          <li>• Real-time Cart & Inventory</li>
                          <li>• Stripe Payment Integration</li>
                          <li>• PWA with Offline Support</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-teal-400 mb-3">Backend Systems</h4>
                        <ul className="space-y-2 text-sm text-gray-400">
                          <li>• Node.js Microservices</li>
                          <li>• Redis Caching Layer</li>
                          <li>• Kafka Event Streaming</li>
                          <li>• Elasticsearch Product Search</li>
                        </ul>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="text-sm font-semibold text-teal-400">Key Achievements</h4>
                      <ul className="space-y-2 text-sm text-gray-400">
                        <li>• 99.99% Uptime with Blue-Green Deployment</li>
                        <li>• 300ms Average API Response Time</li>
                        <li>• 45% Reduction in Infrastructure Costs</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-black/30 rounded-xl p-6">
                    <Image
                      src="/hubble-image.webp" // Path relative to the public directory
                      alt="A description of the image"
                      width={800} // Required for local images to prevent layout shift
                      height={300} // Required for local images to prevent layout shift
                    />
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap justify-center gap-2 md:gap-3 mb-6 md:mb-8">
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Javascript</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm">Express.js</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-teal-500/10 rounded-full text-teal-400 text-xs md:text-sm">Node.js</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Mongo DB</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm">AWS</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">TypeScript</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">NATS</span>
                <span className="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Docker</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
