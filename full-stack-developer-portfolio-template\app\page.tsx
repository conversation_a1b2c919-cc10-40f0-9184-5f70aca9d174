"use client";

import { useState } from "react";
import HeroSection from "./components/HeroSection";
import ProjectsSection from "./components/ProjectsSection";
import ContactSection from "./components/ContactSection";
import ExperienceSection from "./components/ExperienceSection";
import Loader from "./components/Loader";

export default function FullStackPortfolio() {
  const [isLoading, setIsLoading] = useState(true);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return <Loader onLoadingComplete={handleLoadingComplete} />;
  }

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-950 to-black text-white">
      <HeroSection />
      <ExperienceSection />
      {/* <ProjectsSection /> */}
      <ContactSection />
    </main>
  );
}
