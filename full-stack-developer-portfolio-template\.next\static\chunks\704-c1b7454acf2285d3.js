"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[704],{5565:(t,e,i)=>{i.d(e,{default:()=>r.a});var n=i(4146),r=i.n(n)},7970:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Image",{enumerable:!0,get:function(){return w}});let n=i(306),r=i(9955),s=i(5155),o=r._(i(2115)),a=n._(i(7650)),l=n._(i(6107)),u=i(666),h=i(1159),d=i(3621);i(2363);let c=i(3576),p=n._(i(5514)),f=i(5353),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(t,e,i,n,r,s,o){let a=null==t?void 0:t.src;t&&t["data-loaded-src"]!==a&&(t["data-loaded-src"]=a,("decode"in t?t.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(t.parentElement&&t.isConnected){if("empty"!==e&&r(!0),null==i?void 0:i.current){let e=new Event("load");Object.defineProperty(e,"target",{writable:!1,value:t});let n=!1,r=!1;i.current({...e,nativeEvent:e,currentTarget:t,target:t,isDefaultPrevented:()=>n,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{n=!0,e.preventDefault()},stopPropagation:()=>{r=!0,e.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(t)}}))}function v(t){return o.use?{fetchPriority:t}:{fetchpriority:t}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let y=(0,o.forwardRef)((t,e)=>{let{src:i,srcSet:n,sizes:r,height:a,width:l,decoding:u,className:h,style:d,fetchPriority:c,placeholder:p,loading:m,unoptimized:y,fill:x,onLoadRef:w,onLoadingCompleteRef:b,setBlurComplete:P,setShowAltText:T,sizesInput:S,onLoad:A,onError:M,...E}=t,C=(0,o.useCallback)(t=>{t&&(M&&(t.src=t.src),t.complete&&g(t,p,w,b,P,y,S))},[i,p,w,b,P,M,y,S]),k=(0,f.useMergedRef)(e,C);return(0,s.jsx)("img",{...E,...v(c),loading:m,width:l,height:a,decoding:u,"data-nimg":x?"fill":"1",className:h,style:d,sizes:r,srcSet:n,src:i,ref:k,onLoad:t=>{g(t.currentTarget,p,w,b,P,y,S)},onError:t=>{T(!0),"empty"!==p&&P(!0),M&&M(t)}})});function x(t){let{isAppRouter:e,imgAttributes:i}=t,n={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...v(i.fetchPriority)};return e&&a.default.preload?(a.default.preload(i.src,n),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...n},"__nimg-"+i.src+i.srcSet+i.sizes)})}let w=(0,o.forwardRef)((t,e)=>{let i=(0,o.useContext)(c.RouterContext),n=(0,o.useContext)(d.ImageConfigContext),r=(0,o.useMemo)(()=>{var t;let e=m||n||h.imageConfigDefault,i=[...e.deviceSizes,...e.imageSizes].sort((t,e)=>t-e),r=e.deviceSizes.sort((t,e)=>t-e),s=null==(t=e.qualities)?void 0:t.sort((t,e)=>t-e);return{...e,allSizes:i,deviceSizes:r,qualities:s}},[n]),{onLoad:a,onLoadingComplete:l}=t,f=(0,o.useRef)(a);(0,o.useEffect)(()=>{f.current=a},[a]);let g=(0,o.useRef)(l);(0,o.useEffect)(()=>{g.current=l},[l]);let[v,w]=(0,o.useState)(!1),[b,P]=(0,o.useState)(!1),{props:T,meta:S}=(0,u.getImgProps)(t,{defaultLoader:p.default,imgConf:r,blurComplete:v,showAltText:b});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{...T,unoptimized:S.unoptimized,placeholder:S.placeholder,fill:S.fill,onLoadRef:f,onLoadingCompleteRef:g,setBlurComplete:w,setShowAltText:P,sizesInput:t.sizes,ref:e}),S.priority?(0,s.jsx)(x,{isAppRouter:!i,imgAttributes:T}):null]})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},5353:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(2115);function r(t,e){let i=(0,n.useRef)(()=>{}),r=(0,n.useRef)(()=>{});return(0,n.useMemo)(()=>t&&e?n=>{null===n?(i.current(),r.current()):(i.current=s(t,n),r.current=s(e,n))}:t||e,[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},3003:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=i(306)._(i(2115)).default.createContext({})},675:(t,e)=>{function i(t){let{ampFirst:e=!1,hybrid:i=!1,hasQuery:n=!1}=void 0===t?{}:t;return e||i&&n}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isInAmpMode",{enumerable:!0,get:function(){return i}})},666:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImgProps",{enumerable:!0,get:function(){return a}}),i(2363);let n=i(5859),r=i(1159);function s(t){return void 0!==t.default}function o(t){return void 0===t?t:"number"==typeof t?Number.isFinite(t)?t:NaN:"string"==typeof t&&/^[0-9]+$/.test(t)?parseInt(t,10):NaN}function a(t,e){var i,a;let l,u,h,{src:d,sizes:c,unoptimized:p=!1,priority:f=!1,loading:m,className:g,quality:v,width:y,height:x,fill:w=!1,style:b,overrideSrc:P,onLoad:T,onLoadingComplete:S,placeholder:A="empty",blurDataURL:M,fetchPriority:E,decoding:C="async",layout:k,objectFit:V,objectPosition:j,lazyBoundary:D,lazyRoot:R,...L}=t,{imgConf:O,showAltText:B,blurComplete:F,defaultLoader:I}=e,W=O||r.imageConfigDefault;if("allSizes"in W)l=W;else{let t=[...W.deviceSizes,...W.imageSizes].sort((t,e)=>t-e),e=W.deviceSizes.sort((t,e)=>t-e),n=null==(i=W.qualities)?void 0:i.sort((t,e)=>t-e);l={...W,allSizes:t,deviceSizes:e,qualities:n}}if(void 0===I)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let _=L.loader||I;delete L.loader,delete L.srcSet;let U="__next_img_default"in _;if(U){if("custom"===l.loader)throw Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let t=_;_=e=>{let{config:i,...n}=e;return t(n)}}if(k){"fill"===k&&(w=!0);let t={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[k];t&&(b={...b,...t});let e={responsive:"100vw",fill:"100vw"}[k];e&&!c&&(c=e)}let N="",z=o(y),G=o(x);if((a=d)&&"object"==typeof a&&(s(a)||void 0!==a.src)){let t=s(d)?d.default:d;if(!t.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(t));if(!t.height||!t.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(t));if(u=t.blurWidth,h=t.blurHeight,M=M||t.blurDataURL,N=t.src,!w){if(z||G){if(z&&!G){let e=z/t.width;G=Math.round(t.height*e)}else if(!z&&G){let e=G/t.height;z=Math.round(t.width*e)}}else z=t.width,G=t.height}}let $=!f&&("lazy"===m||void 0===m);(!(d="string"==typeof d?d:N)||d.startsWith("data:")||d.startsWith("blob:"))&&(p=!0,$=!1),l.unoptimized&&(p=!0),U&&!l.dangerouslyAllowSVG&&d.split("?",1)[0].endsWith(".svg")&&(p=!0);let q=o(v),H=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:V,objectPosition:j}:{},B?{}:{color:"transparent"},b),X=F||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:z,heightInt:G,blurWidth:u,blurHeight:h,blurDataURL:M||"",objectFit:H.objectFit})+'")':'url("'+A+'")',Y=X?{backgroundSize:H.objectFit||"cover",backgroundPosition:H.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},K=function(t){let{config:e,src:i,unoptimized:n,width:r,quality:s,sizes:o,loader:a}=t;if(n)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(t,e,i){let{deviceSizes:n,allSizes:r}=t;if(i){let t=/(^|\s)(1?\d?\d)vw/g,e=[];for(let n;n=t.exec(i);n)e.push(parseInt(n[2]));if(e.length){let t=.01*Math.min(...e);return{widths:r.filter(e=>e>=n[0]*t),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof e?{widths:n,kind:"w"}:{widths:[...new Set([e,2*e].map(t=>r.find(e=>e>=t)||r[r.length-1]))],kind:"x"}}(e,r,o),h=l.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:l.map((t,n)=>a({config:e,src:i,quality:s,width:t})+" "+("w"===u?t:n+1)+u).join(", "),src:a({config:e,src:i,quality:s,width:l[h]})}}({config:l,src:d,unoptimized:p,width:z,quality:q,sizes:c,loader:_});return{props:{...L,loading:$?"lazy":m,fetchPriority:E,width:z,height:G,decoding:C,className:g,style:{...H,...Y},sizes:K.sizes,srcSet:K.srcSet,src:P||K.src},meta:{unoptimized:p,priority:f,placeholder:A,fill:w}}}},6107:(t,e,i)=>{var n=i(2818);Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return g},defaultHead:function(){return c}});let r=i(306),s=i(9955),o=i(5155),a=s._(i(2115)),l=r._(i(1172)),u=i(3003),h=i(1147),d=i(675);function c(t){void 0===t&&(t=!1);let e=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return t||e.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),e}function p(t,e){return"string"==typeof e||"number"==typeof e?t:e.type===a.default.Fragment?t.concat(a.default.Children.toArray(e.props.children).reduce((t,e)=>"string"==typeof e||"number"==typeof e?t:t.concat(e),[])):t.concat(e)}i(2363);let f=["name","httpEquiv","charSet","itemProp"];function m(t,e){let{inAmpMode:i}=e;return t.reduce(p,[]).reverse().concat(c(i).reverse()).filter(function(){let t=new Set,e=new Set,i=new Set,n={};return r=>{let s=!0,o=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){o=!0;let e=r.key.slice(r.key.indexOf("$")+1);t.has(e)?s=!1:t.add(e)}switch(r.type){case"title":case"base":e.has(r.type)?s=!1:e.add(r.type);break;case"meta":for(let t=0,e=f.length;t<e;t++){let e=f[t];if(r.props.hasOwnProperty(e)){if("charSet"===e)i.has(e)?s=!1:i.add(e);else{let t=r.props[e],i=n[e]||new Set;("name"!==e||!o)&&i.has(t)?s=!1:(i.add(t),n[e]=i)}}}}return s}}()).reverse().map((t,e)=>{let r=t.key||e;if(n.env.__NEXT_OPTIMIZE_FONTS&&!i&&"link"===t.type&&t.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(e=>t.props.href.startsWith(e))){let e={...t.props||{}};return e["data-href"]=e.href,e.href=void 0,e["data-optimized-fonts"]=!0,a.default.cloneElement(t,e)}return a.default.cloneElement(t,{key:r})})}let g=function(t){let{children:e}=t,i=(0,a.useContext)(u.AmpStateContext),n=(0,a.useContext)(h.HeadManagerContext);return(0,o.jsx)(l.default,{reduceComponentsToState:m,headManager:n,inAmpMode:(0,d.isInAmpMode)(i),children:e})};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},5859:(t,e)=>{function i(t){let{widthInt:e,heightInt:i,blurWidth:n,blurHeight:r,blurDataURL:s,objectFit:o}=t,a=n?40*n:e,l=r?40*r:i,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},3621:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ImageConfigContext",{enumerable:!0,get:function(){return s}});let n=i(306)._(i(2115)),r=i(1159),s=n.default.createContext(r.imageConfigDefault)},1159:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return n}});let i=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},4146:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return l},getImageProps:function(){return a}});let n=i(306),r=i(666),s=i(7970),o=n._(i(5514));function a(t){let{props:e}=(0,r.getImgProps)(t,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,i]of Object.entries(e))void 0===i&&delete e[t];return{props:e}}let l=s.Image},5514:(t,e)=>{function i(t){var e;let{config:i,src:n,width:r,quality:s}=t,o=s||(null==(e=i.qualities)?void 0:e.reduce((t,e)=>Math.abs(e-75)<Math.abs(t-75)?e:t))||75;return i.path+"?url="+encodeURIComponent(n)+"&w="+r+"&q="+o+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return n}}),i.__next_img_default=!0;let n=i},3576:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"RouterContext",{enumerable:!0,get:function(){return n}});let n=i(306)._(i(2115)).default.createContext(null)},1172:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return a}});let n=i(2115),r="undefined"==typeof window,s=r?()=>{}:n.useLayoutEffect,o=r?()=>{}:n.useEffect;function a(t){let{headManager:e,reduceComponentsToState:i}=t;function a(){if(e&&e.mountedInstances){let r=n.Children.toArray(Array.from(e.mountedInstances).filter(Boolean));e.updateHead(i(r,t))}}if(r){var l;null==e||null==(l=e.mountedInstances)||l.add(t.children),a()}return s(()=>{var i;return null==e||null==(i=e.mountedInstances)||i.add(t.children),()=>{var i;null==e||null==(i=e.mountedInstances)||i.delete(t.children)}}),s(()=>(e&&(e._pendingUpdate=a),()=>{e&&(e._pendingUpdate=a)})),o(()=>(e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null),()=>{e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null)})),null}},7249:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},3362:(t,e,i)=>{i.d(e,{P:()=>rI});var n=i(2115);let r=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],s=new Set(r),o=t=>180*t/Math.PI,a=t=>u(o(Math.atan2(t[1],t[0]))),l={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:a,rotateZ:a,skewX:t=>o(Math.atan(t[1])),skewY:t=>o(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},u=t=>((t%=360)<0&&(t+=360),t),h=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),d=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),c={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:h,scaleY:d,scale:t=>(h(t)+d(t))/2,rotateX:t=>u(o(Math.atan2(t[6],t[5]))),rotateY:t=>u(o(Math.atan2(-t[2],t[0]))),rotateZ:a,rotate:a,skewX:t=>o(Math.atan(t[4])),skewY:t=>o(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function p(t){return t.includes("scale")?1:0}function f(t,e){let i,n;if(!t||"none"===t)return p(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=c,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=l,n=e}if(!n)return p(e);let s=i[e],o=n[1].split(",").map(g);return"function"==typeof s?s(o):o[s]}let m=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return f(i,e)};function g(t){return parseFloat(t.trim())}var v=i(7309);function y({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}var x=i(1607);function w(t){return void 0===t||1===t}function b({scale:t,scaleX:e,scaleY:i}){return!w(t)||!w(e)||!w(i)}function P(t){return b(t)||T(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function T(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function S(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function A(t,e=0,i=1,n,r){t.min=S(t.min,e,i,n,r),t.max=S(t.max,e,i,n,r)}function M(t,{x:e,y:i}){A(t.x,e.translate,e.scale,e.originPoint),A(t.y,i.translate,i.scale,i.originPoint)}function E(t,e){t.min=t.min+e,t.max=t.max+e}function C(t,e,i,n,r=.5){let s=(0,x.k)(t.min,t.max,r);A(t,e,i,s,n)}function k(t,e){C(t.x,e.x,e.scaleX,e.scale,e.originX),C(t.y,e.y,e.scaleY,e.scale,e.originY)}function V(t,e){return y(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let j=new Set(["width","height","top","left","right","bottom",...r]);var D=i(2264),R=i(3137);let L=t=>e=>e.test(t),O=[D.ai,R.px,R.KN,R.uj,R.vw,R.vh,{test:t=>"auto"===t,parse:t=>t}],B=t=>O.find(L(t));var F=i(5107);let I=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),W=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,_=t=>t===D.ai||t===R.px,U=new Set(["x","y","z"]),N=r.filter(t=>!U.has(t)),z={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};z.translateX=z.x,z.translateY=z.y;var G=i(3932);let $=new Set,q=!1,H=!1,X=!1;function Y(){if(H){let t=Array.from($).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return N.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}H=!1,q=!1,$.forEach(t=>t.complete(X)),$.clear()}function K(){$.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(H=!0)})}class Q{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?($.add(this),q||(q=!0,G.Gt.read(K),G.Gt.resolveKeyframes(Y))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),$.delete(this)}cancel(){"scheduled"===this.state&&($.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let Z=t=>/^0[^.\s]+$/u.test(t);var J=i(663),tt=i(4885);let te=new Set(["brightness","contrast","saturate","opacity"]);function ti(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tt.S)||[];if(!n)return t;let r=i.replace(n,""),s=te.has(e)?1:0;return n!==i&&(s*=100),e+"("+s+r+")"}let tn=/\b([a-z-]*)\(.*?\)/gu,tr={...J.f,getAnimatableNone:t=>{let e=t.match(tn);return e?e.map(ti).join(" "):t}};var ts=i(8207);let to={...D.ai,transform:Math.round},ta={rotate:R.uj,rotateX:R.uj,rotateY:R.uj,rotateZ:R.uj,scale:D.hs,scaleX:D.hs,scaleY:D.hs,scaleZ:D.hs,skew:R.uj,skewX:R.uj,skewY:R.uj,distance:R.px,translateX:R.px,translateY:R.px,translateZ:R.px,x:R.px,y:R.px,z:R.px,perspective:R.px,transformPerspective:R.px,opacity:D.X4,originX:R.gQ,originY:R.gQ,originZ:R.px},tl={borderWidth:R.px,borderTopWidth:R.px,borderRightWidth:R.px,borderBottomWidth:R.px,borderLeftWidth:R.px,borderRadius:R.px,radius:R.px,borderTopLeftRadius:R.px,borderTopRightRadius:R.px,borderBottomRightRadius:R.px,borderBottomLeftRadius:R.px,width:R.px,maxWidth:R.px,height:R.px,maxHeight:R.px,top:R.px,right:R.px,bottom:R.px,left:R.px,padding:R.px,paddingTop:R.px,paddingRight:R.px,paddingBottom:R.px,paddingLeft:R.px,margin:R.px,marginTop:R.px,marginRight:R.px,marginBottom:R.px,marginLeft:R.px,backgroundPositionX:R.px,backgroundPositionY:R.px,...ta,zIndex:to,fillOpacity:D.X4,strokeOpacity:D.X4,numOctaves:to},tu={...tl,color:ts.y,backgroundColor:ts.y,outlineColor:ts.y,fill:ts.y,stroke:ts.y,borderColor:ts.y,borderTopColor:ts.y,borderRightColor:ts.y,borderBottomColor:ts.y,borderLeftColor:ts.y,filter:tr,WebkitFilter:tr},th=t=>tu[t];function td(t,e){let i=th(t);return i!==tr&&(i=J.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tc=new Set(["auto","none","0"]);class tp extends Q{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),(0,v.p)(n))){let r=function t(e,i,n=1){(0,F.V)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[r,s]=function(t){let e=W.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return I(t)?parseFloat(t):t}return(0,v.p)(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!j.has(i)||2!==t.length)return;let[n,r]=t,s=B(n),o=B(r);if(s!==o){if(_(s)&&_(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else z[i]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||Z(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!tc.has(e)&&(0,J.V)(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=td(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=z[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=z[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tf=t=>!!(t&&t.getVelocity);var tm=i(5850),tg=i(9421);let tv=[...O,ts.y,J.f],ty=t=>tv.find(L(t)),{schedule:tx}=(0,i(506).I)(queueMicrotask,!1);var tw=i(2539);let tb={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},tP={};for(let t in tb)tP[t]={isEnabled:e=>tb[t].some(t=>!!e[t])};let tT=()=>({translate:0,scale:1,origin:0,originPoint:0}),tS=()=>({x:tT(),y:tT()}),tA=()=>({min:0,max:0}),tM=()=>({x:tA(),y:tA()});var tE=i(5687);let tC={current:null},tk={current:!1},tV=new WeakMap;function tj(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function tD(t){return"string"==typeof t||Array.isArray(t)}let tR=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tL=["initial",...tR];function tO(t){return tj(t.animate)||tL.some(e=>tD(t[e]))}function tB(t){return!!(tO(t)||t.variants)}function tF(t,e,i,n){if("function"==typeof e||("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e)){let[r,s]=function(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}(n);e=e(void 0!==i?i:t.custom,r,s)}return e}let tI=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tW{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Q,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tm.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,G.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=tO(e),this.isVariantNode=tB(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&tf(e)&&e.set(a[t])}}mount(t){this.current=t,tV.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),tk.current||function(){if(tk.current=!0,tE.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>tC.current=t.matches;t.addEventListener("change",e),e()}else tC.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||tC.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,G.WG)(this.notifyUpdate),(0,G.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=s.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&G.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in tP){let e=tP[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tM()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<tI.length;e++){let i=tI[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(tf(r))t.addValue(n,r);else if(tf(s))t.addValue(n,(0,tg.OQ)(r,{owner:t}));else if(s!==r){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,(0,tg.OQ)(void 0!==e?e:r,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,tg.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(I(i)||Z(i))?i=parseFloat(i):!ty(i)&&J.f.test(e)&&(i=td(t,e)),this.setBaseTarget(t,tf(i)?i.get():i)),tf(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=tF(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tf(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new tw.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){tx.render(this.render)}}class t_ extends tW{constructor(){super(...arguments),this.KeyframeResolver=tp}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tf(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let tU=(t,e)=>e&&"number"==typeof t?e.transform(t):t,tN={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tz=r.length;function tG(t,e,i){let{style:n,vars:o,transformOrigin:a}=t,l=!1,u=!1;for(let t in e){let i=e[t];if(s.has(t)){l=!0;continue}if((0,v.j)(t)){o[t]=i;continue}{let e=tU(i,tl[t]);t.startsWith("origin")?(u=!0,a[t]=e):n[t]=e}}if(!e.transform&&(l||i?n.transform=function(t,e,i){let n="",s=!0;for(let o=0;o<tz;o++){let a=r[o],l=t[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===(a.startsWith("scale")?1:0):0===parseFloat(l))||i){let t=tU(l,tl[a]);if(!u){s=!1;let e=tN[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;n.transformOrigin=`${t} ${e} ${i}`}}function t$(t,{style:e,vars:i},n,r){let s;let o=t.style;for(s in e)o[s]=e[s];for(s in r?.applyProjectionStyles(o,n),i)o.setProperty(s,i[s])}let tq={};function tH(t,{layout:e,layoutId:i}){return s.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!tq[t]||"opacity"===t)}function tX(t,e,i){let{style:n}=t,r={};for(let s in n)(tf(n[s])||e.style&&tf(e.style[s])||tH(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}class tY extends t_{constructor(){super(...arguments),this.type="html",this.renderInstance=t$}readValueFromInstance(t,e){if(s.has(e))return this.projection?.isProjecting?p(e):m(t,e);{let i=window.getComputedStyle(t),n=((0,v.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return V(t,e)}build(t,e,i){tG(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return tX(t,e,i)}}let tK=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tQ={offset:"stroke-dashoffset",array:"stroke-dasharray"},tZ={offset:"strokeDashoffset",array:"strokeDasharray"};function tJ(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(tG(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?tQ:tZ;t[s.offset]=R.px.transform(-n);let o=R.px.transform(e),a=R.px.transform(i);t[s.array]=`${o} ${a}`}(d,r,s,o,!1)}let t0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),t1=t=>"string"==typeof t&&"svg"===t.toLowerCase();function t5(t,e,i){let n=tX(t,e,i);for(let i in t)(tf(t[i])||tf(e[i]))&&(n[-1!==r.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}class t2 extends t_{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tM}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(s.has(e)){let t=th(e);return t&&t.default||0}return e=t0.has(e)?e:tK(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return t5(t,e,i)}build(t,e,i){tJ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){!function(t,e,i,n){for(let i in t$(t,e,void 0,n),e.attrs)t.setAttribute(t0.has(i)?i:tK(i),e.attrs[i])}(t,e,0,n)}mount(t){this.isSVGTag=t1(t.tagName),super.mount(t)}}let t3=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function t4(t){if("string"!=typeof t||t.includes("-"));else if(t3.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var t6=i(5155);let t9=(0,n.createContext)({}),t8=(0,n.createContext)({strict:!1});var t7=i(7249);let et=(0,n.createContext)({});function ee(t){return Array.isArray(t)?t.join(" "):t}let ei=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function en(t,e,i){for(let n in e)tf(e[n])||tH(n,i)||(t[n]=e[n])}let er=()=>({...ei(),attrs:{}}),es=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eo(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||es.has(t)}let ea=t=>!eo(t);try{!function(t){"function"==typeof t&&(ea=e=>e.startsWith("on")?!eo(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let el=(0,n.createContext)(null);var eu=i(9234);function eh(t){return tf(t)?t.get():t}let ed=t=>(e,i)=>{let r=(0,n.useContext)(et),s=(0,n.useContext)(el),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,r){return{latestValues:function(t,e,i,n){let r={},s=n(t,{});for(let t in s)r[t]=eh(s[t]);let{initial:o,animate:a}=t,l=tO(t),u=tB(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,d=(h=h||!1===o)?a:o;if(d&&"boolean"!=typeof d&&!tj(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let n=tF(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,n,r,t),renderState:e()}})(t,e,r,s);return i?o():(0,eu.M)(o)},ec=ed({scrapeMotionValuesFromProps:tX,createRenderState:ei}),ep=ed({scrapeMotionValuesFromProps:t5,createRenderState:er}),ef=Symbol.for("motionComponentSymbol");function em(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let eg="data-"+tK("framerAppearId"),ev=(0,n.createContext)({});var ey=i(5403);function ex(t){var e,i;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;s&&function(t){for(let e in t)tP[e]={...tP[e],...t[e]}}(s);let a=t4(t)?ep:ec;function l(e,i){var s;let l;let u={...(0,n.useContext)(t7.Q),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,n.useContext)(t9).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:h}=u,d=function(t){let{initial:e,animate:i}=function(t,e){if(tO(t)){let{initial:e,animate:i}=t;return{initial:!1===e||tD(e)?e:void 0,animate:tD(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,n.useContext)(et));return(0,n.useMemo)(()=>({initial:e,animate:i}),[ee(e),ee(i)])}(e),c=a(e,h);if(!h&&tE.B){(0,n.useContext)(t8).strict;let e=function(t){let{drag:e,layout:i}=tP;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);l=e.MeasureLayout,d.visualElement=function(t,e,i,r,s){let{visualElement:o}=(0,n.useContext)(et),a=(0,n.useContext)(t8),l=(0,n.useContext)(el),u=(0,n.useContext)(t7.Q).reducedMotion,h=(0,n.useRef)(null);r=r||a.renderer,!h.current&&r&&(h.current=r(t,{visualState:e,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=h.current,c=(0,n.useContext)(ev);d&&!d.projection&&s&&("html"===d.type||"svg"===d.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&em(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,s,c);let p=(0,n.useRef)(!1);(0,n.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let f=i[eg],m=(0,n.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,ey.E)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),m.current&&d.animationState&&d.animationState.animateChanges())}),(0,n.useEffect)(()=>{d&&(!m.current&&d.animationState&&d.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),m.current=!1),d.enteringChildren=void 0)}),d}(t,c,u,o,e.ProjectionNode)}return(0,t6.jsxs)(et.Provider,{value:d,children:[l&&d.visualElement?(0,t6.jsx)(l,{visualElement:d.visualElement,...u}):null,function(t,e,i,{latestValues:r},s,o=!1){let a=(t4(t)?function(t,e,i,r){let s=(0,n.useMemo)(()=>{let i=er();return tJ(i,e,t1(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};en(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return en(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,n.useMemo)(()=>{let i=ei();return tG(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(e,r,s,t),l=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(ea(r)||!0===i&&eo(r)||!e&&!eo(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(e,"string"==typeof t,o),u=t!==n.Fragment?{...l,...a,ref:i}:{},{children:h}=e,d=(0,n.useMemo)(()=>tf(h)?h.get():h,[h]);return(0,n.createElement)(t,{...u,children:d})}(t,e,(s=d.visualElement,(0,n.useCallback)(t=>{t&&c.onMount&&c.onMount(t),s&&(t?s.mount(t):s.unmount()),i&&("function"==typeof i?i(t):em(i)&&(i.current=t))},[s])),c,h,r)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!==(i=null!==(e=t.displayName)&&void 0!==e?e:t.name)&&void 0!==i?i:"",")"));let u=(0,n.forwardRef)(l);return u[ef]=t,u}function ew(t,e,i){let n=t.getProps();return tF(n,e,void 0!==i?i:n.custom,t)}function eb(t,e){return t?.[e]??t?.default??t}let eP=t=>Array.isArray(t);var eT=i(4148);function eS(t,e){let i=t.getValue("willChange");if(tf(i)&&i.add)return i.add(e);if(!i&&eT.W.WillChange){let i=new eT.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function eA(t){t.duration=0,t.type}var eM=i(1046),eE=i(5821);let eC=t=>1e3*t,ek=t=>t/1e3,eV={layout:0,mainThread:0,waapi:0};var ej=i(9674);let eD=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>G.Gt.update(e,t),stop:()=>(0,G.WG)(e),now:()=>G.uv.isProcessing?G.uv.timestamp:tm.k.now()}},eR=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function eL(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}var eO=i(3700);function eB(t,e,i){let n=Math.max(e-5,0);return(0,eO.f)(i-t(n),e-n)}let eF={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eI(t,e){return t*Math.sqrt(1-e*e)}let eW=["duration","bounce"],e_=["stiffness","damping","mass"];function eU(t,e){return e.some(e=>void 0!==t[e])}function eN(t=eF.visualDuration,e=eF.bounce){let i;let n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:eF.velocity,stiffness:eF.stiffness,damping:eF.damping,mass:eF.mass,isResolvedFromDuration:!1,...t};if(!eU(t,e_)&&eU(t,eW)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*(0,eE.q)(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:eF.mass,stiffness:n,damping:r}}else{let i=function({duration:t=eF.duration,bounce:e=eF.bounce,velocity:i=eF.velocity,mass:n=eF.mass}){let r,s;(0,F.$)(t<=eC(eF.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=(0,eE.q)(eF.minDamping,eF.maxDamping,o),t=(0,eE.q)(eF.minDuration,eF.maxDuration,ek(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/eI(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=eI(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=eC(t),isNaN(a))return{stiffness:eF.stiffness,damping:eF.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:eF.mass}).isResolvedFromDuration=!0}}return e}({...n,velocity:-ek(n.velocity||0)}),m=p||0,g=h/(2*Math.sqrt(u*d)),v=a-o,y=ek(Math.sqrt(u/d)),x=5>Math.abs(v);if(r||(r=x?eF.restSpeed.granular:eF.restSpeed.default),s||(s=x?eF.restDelta.granular:eF.restDelta.default),g<1){let t=eI(y,g);i=e=>a-Math.exp(-g*y*e)*((m+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-y*t)*(v+(m+y*v)*t);else{let t=y*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*y*e),n=Math.min(t*e,300);return a-i*((m+g*y*v)*Math.sinh(n)+t*v*Math.cosh(n))/t}}let w={calculatedDuration:f&&c||null,next:t=>{let e=i(t);if(f)l.done=t>=c;else{let n=0===t?m:0;g<1&&(n=0===t?eC(m):eB(i,t,e));let o=Math.abs(n)<=r,u=Math.abs(a-e)<=s;l.done=o&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(eL(w),2e4),e=eR(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function ez({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],f={done:!1,value:p},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,y=p+v,x=void 0===o?y:o(y);x!==y&&(v=x-p);let w=t=>-v*Math.exp(-t/n),b=t=>x+w(t),P=t=>{let e=w(t),i=b(t);f.done=Math.abs(e)<=u,f.value=f.done?x:i},T=t=>{m(f.value)&&(d=t,c=eN({keyframes:[f.value,g(f.value)],velocity:eB(b,t,f.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,P(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||P(t),f)}}}eN.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(eL(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:ek(r)}}(t,100,eN);return t.ease=e.ease,t.duration=eC(e.duration),t.type="keyframes",t};var eG=i(6054);let e$=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function eq(t,e,i,n){if(t===e&&i===n)return eG.l;let r=e=>(function(t,e,i,n,r){let s,o;let a=0;do(s=e$(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:e$(r(t),e,n)}let eH=eq(.42,0,1,1),eX=eq(0,0,.58,1),eY=eq(.42,0,.58,1),eK=t=>Array.isArray(t)&&"number"!=typeof t[0],eQ=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,eZ=t=>e=>1-t(1-e),eJ=eq(.33,1.53,.69,.99),e0=eZ(eJ),e1=eQ(e0),e5=t=>(t*=2)<1?.5*e0(t):.5*(2-Math.pow(2,-10*(t-1))),e2=t=>1-Math.sin(Math.acos(t)),e3=eZ(e2),e4=eQ(e2),e6=t=>Array.isArray(t)&&"number"==typeof t[0],e9={linear:eG.l,easeIn:eH,easeInOut:eY,easeOut:eX,circIn:e2,circInOut:e4,circOut:e3,backIn:e0,backInOut:e1,backOut:eJ,anticipate:e5},e8=t=>"string"==typeof t,e7=t=>{if(e6(t)){(0,F.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,r]=t;return eq(e,i,n,r)}return e8(t)?((0,F.V)(void 0!==e9[t],`Invalid easing type '${t}'`,"invalid-easing-type"),e9[t]):t};var it=i(1136),ie=i(6421);function ii({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=eK(n)?n.map(e7):e7(n),s={done:!1,value:e[0]},o=(i&&i.length===e.length?i:(0,ie.Z)(e)).map(e=>e*t),a=(0,it.G)(o,e,{ease:Array.isArray(r)?r:e.map(()=>r||eY).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=a(e),s.done=e>=t,s)}}let ir=t=>null!==t;function is(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ir),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let io={decay:ez,inertia:ez,tween:ii,keyframes:ii,spring:eN};function ia(t){"string"==typeof t.type&&(t.type=io[t.type])}class il{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let iu=t=>t/100;class ih extends il{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==tm.k.now()&&this.tick(tm.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},eV.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ia(t);let{type:e=ii,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||ii;a!==ii&&"number"!=typeof o[0]&&(this.mixKeyframes=(0,eM.F)(iu,(0,ej.j)(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=eL(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let y=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=s)),y=(0,eE.q)(0,1,i)*o}let w=v?{done:!1,value:u[0]}:x.next(y);r&&(w.value=r(w.value));let{done:b}=w;v||null===a||(b=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return P&&p!==ez&&(w.value=is(u,this.options,m,this.speed)),f&&f(w.value),P&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return ek(this.calculatedDuration)}get time(){return ek(this.currentTime)}set time(t){t=eC(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tm.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=ek(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=eD,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tm.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,eV.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let id=t=>t.startsWith("--");var ic=i(5425),ip=i(5853),im=i(4996);let ig={},iv=function(t,e){let i=(0,im.p)(t);return()=>ig[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),iy=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,ix={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:iy([0,.65,.55,1]),circOut:iy([.55,0,1,.45]),backIn:iy([.31,.01,.66,-.59]),backOut:iy([.33,1.53,.69,.99])};function iw(t){return"function"==typeof t&&"applyToOptions"in t}class ib extends il{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,(0,F.V)("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return iw(t)&&iv()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?iv()?eR(e,i):"ease-out":e6(e)?iy(e):Array.isArray(e)?e.map(e=>t(e,i)||ix.easeOut):ix[e]}(a,r);Array.isArray(d)&&(h.easing=d),ip.Q.value&&eV.waapi++;let c={delay:n,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return ip.Q.value&&p.finished.finally(()=>{eV.waapi--}),p}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=is(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){id(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return ek(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return ek(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=eC(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&(0,ic.J)())?(this.animation.timeline=t,eG.l):e(this)}}let iP={anticipate:e5,backInOut:e1,circInOut:e4};class iT extends ib{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in iP&&(t.ease=iP[t.ease])}(t),ia(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let o=new ih({...s,autoplay:!1}),a=eC(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let iS=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(J.f.test(t)||"0"===t)&&!t.startsWith("url(")),iA=new Set(["opacity","clipPath","filter","transform"]),iM=(0,im.p)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class iE extends il{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tm.k.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||Q;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=tm.k.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=iS(r,e),a=iS(s,e);return(0,F.$)(o===a,`You are trying to animate ${e} from "${r}" to "${s}". "${o?s:r}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||iw(i))&&n)}(t,r,s,o)&&((eT.W.instantAnimations||!a)&&u?.(is(t,i,e)),t[0]=t[t.length-1],eA(i),i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return iM()&&i&&iA.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(h)?new iT({...h,element:h.motionValue.owner.current}):new ih(h);d.finished.then(()=>this.notifyFinished()).catch(eG.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),X=!0,K(),Y(),X=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let iC=t=>null!==t,ik={type:"spring",stiffness:500,damping:25,restSpeed:10},iV=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ij={type:"keyframes",duration:.8},iD={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},iR=(t,{keyframes:e})=>e.length>2?ij:s.has(t)?t.startsWith("scale")?iV(e[1]):ik:iD,iL=(t,e,i,n={},r,s)=>o=>{let a=eb(n,t)||{},l=a.delay||n.delay||0,{elapsed:u=0}=n;u-=eC(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(h,iR(t,h)),h.duration&&(h.duration=eC(h.duration)),h.repeatDelay&&(h.repeatDelay=eC(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(eA(h),0!==h.delay||(d=!0)),(eT.W.instantAnimations||eT.W.skipAnimations)&&(d=!0,eA(h),h.delay=0),h.allowFlatten=!a.type&&!a.ease,d&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(iC),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(h.keyframes,a);if(void 0!==t){G.Gt.update(()=>{h.onUpdate(t),h.onComplete()});return}}return a.isSync?new ih(h):new iE(h)};function iO(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=e;n&&(s=n);let l=[],u=r&&t.animationState&&t.animationState.getState()[r];for(let e in a){let n=t.getValue(e,t.latestValues[e]??null),r=a[e];if(void 0===r||u&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(u,e))continue;let o={delay:i,...eb(s||{},e)},h=n.get();if(void 0!==h&&!n.isAnimating&&!Array.isArray(r)&&r===h&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[eg];if(i){let t=window.MotionHandoffAnimation(i,e,G.Gt);null!==t&&(o.startTime=t,d=!0)}}eS(t,e),n.start(iL(e,n,r,t.shouldReduceMotion&&j.has(e)?{type:!1}:o,t,d));let c=n.animation;c&&l.push(c)}return o&&Promise.all(l).then(()=>{G.Gt.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=ew(t,e)||{};for(let e in r={...r,...i}){var s;let i=eP(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,tg.OQ)(i))}}(t,o)})}),l}function iB(t,e,i,n=0,r=1){let s=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),o=t.size,a=(o-1)*n;return"function"==typeof i?i(s,o):1===r?s*n:a-s*n}function iF(t,e,i={}){let n=ew(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(iO(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=0,s=1,o){let a=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),a.push(iF(l,e,{...o,delay:i+("function"==typeof n?0:n)+iB(t.variantChildren,l,n,r,s)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,n,s,o,a,i)}:()=>Promise.resolve(),{when:a}=r;if(!a)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function iI(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}let iW=tL.length,i_=[...tR].reverse(),iU=tR.length;function iN(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iz(){return{animate:iN(!0),whileInView:iN(),whileHover:iN(),whileTap:iN(),whileDrag:iN(),whileFocus:iN(),exit:iN()}}class iG{constructor(t){this.isMounted=!1,this.node=t}update(){}}class i$ extends iG{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>iF(t,e,i)));else if("string"==typeof e)n=iF(t,e,i);else{let r="function"==typeof e?ew(t,e,i.custom):e;n=Promise.all(iO(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iz(),n=!0,r=e=>(i,n)=>{let r=ew(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function s(s){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<iW;t++){let n=tL[t],r=e.props[n];(tD(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},l=[],u=new Set,h={},d=1/0;for(let e=0;e<iU;e++){var c;let p=i_[e],f=i[p],m=void 0!==o[p]?o[p]:a[p],g=tD(m),v=p===s?f.isActive:null;!1===v&&(d=e);let y=m===a[p]&&m!==o[p]&&g;if(y&&n&&t.manuallyAnimateOnMount&&(y=!1),f.protectedKeys={...h},!f.isActive&&null===v||!m&&!f.prevProp||tj(m)||"boolean"==typeof m)continue;let x=(c=f.prevProp,"string"==typeof m?m!==c:!!Array.isArray(m)&&!iI(m,c)),w=x||p===s&&f.isActive&&!y&&g||e>d&&g,b=!1,P=Array.isArray(m)?m:[m],T=P.reduce(r(p),{});!1===v&&(T={});let{prevResolvedValues:S={}}=f,A={...S,...T},M=e=>{w=!0,u.has(e)&&(b=!0,u.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in A){let e=T[t],i=S[t];if(!h.hasOwnProperty(t))(eP(e)&&eP(i)?iI(e,i):e===i)?void 0!==e&&u.has(t)?M(t):f.protectedKeys[t]=!0:null!=e?M(t):u.add(t)}f.prevProp=m,f.prevResolvedValues=T,f.isActive&&(h={...h,...T}),n&&t.blockInitialAnimation&&(w=!1);let E=y&&x,C=!E||b;w&&C&&l.push(...P.map(e=>{let i={type:p};if("string"==typeof e&&n&&!E&&t.manuallyAnimateOnMount&&t.parent){let{parent:n}=t,r=ew(n,e);if(n.enteringChildren&&r){let{delayChildren:e}=r.transition||{};i.delay=iB(n.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(u.size){let e={};if("boolean"!=typeof o.initial){let i=ew(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let p=!!l.length;return n&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(p=!1),n=!1,p?e(l):Promise.resolve()}return{animateChanges:s,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=s(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iz(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();tj(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let iq=0;class iH extends iG{constructor(){super(...arguments),this.id=iq++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let iX={x:!1,y:!1};function iY(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iK=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iQ(t){return{point:{x:t.pageX,y:t.pageY}}}let iZ=t=>e=>iK(e)&&t(e,iQ(e));function iJ(t,e,i,n){return iY(t,e,iZ(i),n)}function i0(t){return t.max-t.min}function i1(t,e,i,n=.5){t.origin=n,t.originPoint=(0,x.k)(e.min,e.max,t.origin),t.scale=i0(i)/i0(e),t.translate=(0,x.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function i5(t,e,i,n){i1(t.x,e.x,i.x,n?n.originX:void 0),i1(t.y,e.y,i.y,n?n.originY:void 0)}function i2(t,e,i){t.min=i.min+e.min,t.max=t.min+i0(e)}function i3(t,e,i){t.min=e.min-i.min,t.max=t.min+i0(e)}function i4(t,e,i){i3(t.x,e.x,i.x),i3(t.y,e.y,i.y)}function i6(t){return[t("x"),t("y")]}let i9=({current:t})=>t?t.ownerDocument.defaultView:null,i8=(t,e)=>Math.abs(t-e);class i7{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=ni(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(i8(t.x,e.x)**2+i8(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=G.uv;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=nt(e,this.transformPagePoint),G.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=ni("pointercancel"===t.type?this.lastMoveEventInfo:nt(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!iK(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let o=nt(iQ(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=G.uv;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,ni(o,this.history)),this.removeListeners=(0,eM.F)(iJ(this.contextWindow,"pointermove",this.handlePointerMove),iJ(this.contextWindow,"pointerup",this.handlePointerUp),iJ(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,G.WG)(this.updatePoint)}}function nt(t,e){return e?{point:e(t.point)}:t}function ne(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ni({point:t},e){return{point:t,delta:ne(t,nn(e)),offset:ne(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=nn(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>eC(.1)));)i--;if(!n)return{x:0,y:0};let s=ek(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function nn(t){return t[t.length-1]}var nr=i(9615);function ns(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function no(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function na(t,e,i){return{min:nl(t,e),max:nl(t,i)}}function nl(t,e){return"number"==typeof t?t:t[e]||0}let nu=new WeakMap;class nh{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tM(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new i7(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iQ(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===i||"y"===i?iX[i]?null:(iX[i]=!0,()=>{iX[i]=!1}):iX.x||iX.y?null:(iX.x=iX.y=!0,()=>{iX.x=iX.y=!1}),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),i6(t=>{let e=this.getAxisMotionValue(t).get()||0;if(R.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=i0(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&G.Gt.postRender(()=>r(t,e)),eS(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>i6(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,distanceThreshold:i,contextWindow:i9(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&G.Gt.postRender(()=>o(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!nd(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?(0,x.k)(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?(0,x.k)(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&em(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:ns(t.x,i,r),y:ns(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:na(t,"left","right"),y:na(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&i6(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!em(e))return!1;let n=e.current;(0,F.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=V(t,i),{scroll:r}=e;return r&&(E(n.x,r.offset.x),E(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:no((t=r.layout.layoutBox).x,s.x),y:no(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=y(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(i6(o=>{if(!nd(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return eS(this.visualElement,t),i.start(iL(t,i,0,e,this.visualElement,!1))}stopAnimation(){i6(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){i6(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){i6(e=>{let{drag:i}=this.getProps();if(!nd(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-(0,x.k)(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!em(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};i6(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=i0(t),r=i0(e);return r>n?i=(0,nr.q)(e.min,e.max-n,t.min):n>r&&(i=(0,nr.q)(t.min,t.max-r,e.min)),(0,eE.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),i6(e=>{if(!nd(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set((0,x.k)(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;nu.set(this.visualElement,this);let t=iJ(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();em(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),G.Gt.read(e);let r=iY(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(i6(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function nd(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class nc extends iG{constructor(t){super(t),this.removeGroupControls=eG.l,this.removeListeners=eG.l,this.controls=new nh(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eG.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let np=t=>(e,i)=>{t&&G.Gt.postRender(()=>t(e,i))};class nf extends iG{constructor(){super(...arguments),this.removePointerDownListener=eG.l}onPointerDown(t){this.session=new i7(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:i9(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:np(t),onStart:np(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&G.Gt.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iJ(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let nm={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ng(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let nv={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!R.px.test(t))return t;t=parseFloat(t)}let i=ng(t,e.target.x),n=ng(t,e.target.y);return`${i}% ${n}%`}},ny=!1;class nx extends n.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;!function(t){for(let e in t)tq[e]=t[e],(0,v.j)(e)&&(tq[e].isCSSVariable=!0)}(nb),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),ny&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nm.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,ny=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||G.Gt.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),tx.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;ny=!0,n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nw(t){let[e,i]=function(t=!0){let e=(0,n.useContext)(el);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:r,register:s}=e,o=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return s(o)},[t]);let a=(0,n.useCallback)(()=>t&&r&&r(o),[o,r,t]);return!i&&r?[!1,a]:[!0]}(),r=(0,n.useContext)(t9);return(0,t6.jsx)(nx,{...t,layoutGroup:r,switchLayoutGroup:(0,n.useContext)(ev),isPresent:e,safeToRemove:i})}let nb={borderRadius:{...nv,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nv,borderTopRightRadius:nv,borderBottomLeftRadius:nv,borderBottomRightRadius:nv,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=J.f.parse(t);if(n.length>5)return t;let r=J.f.createTransformer(t),s="number"!=typeof n[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=(0,x.k)(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nP=i(8091),nT=i(1555);let nS=(t,e)=>t.depth-e.depth;class nA{constructor(){this.children=[],this.isDirty=!1}add(t){(0,nT.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,nT.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nS),this.isDirty=!1,this.children.forEach(t)}}let nM=["TopLeft","TopRight","BottomLeft","BottomRight"],nE=nM.length,nC=t=>"string"==typeof t?parseFloat(t):t,nk=t=>"number"==typeof t||R.px.test(t);function nV(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nj=nR(0,.5,e3),nD=nR(.5,.95,eG.l);function nR(t,e,i){return n=>n<t?0:n>e?1:i((0,nr.q)(t,e,n))}function nL(t,e){t.min=e.min,t.max=e.max}function nO(t,e){nL(t.x,e.x),nL(t.y,e.y)}function nB(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nF(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nI(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(R.KN.test(e)&&(e=parseFloat(e),e=(0,x.k)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,x.k)(s.min,s.max,n);t===s&&(a-=e),t.min=nF(t.min,e,i,a,r),t.max=nF(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nW=["x","scaleX","originX"],n_=["y","scaleY","originY"];function nU(t,e,i,n){nI(t.x,e,nW,i?i.x:void 0,n?n.x:void 0),nI(t.y,e,n_,i?i.y:void 0,n?n.y:void 0)}function nN(t){return 0===t.translate&&1===t.scale}function nz(t){return nN(t.x)&&nN(t.y)}function nG(t,e){return t.min===e.min&&t.max===e.max}function n$(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nq(t,e){return n$(t.x,e.x)&&n$(t.y,e.y)}function nH(t){return i0(t.x)/i0(t.y)}function nX(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nY{constructor(){this.members=[]}add(t){(0,nT.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,nT.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nK={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nQ=["","X","Y","Z"],nZ=0;function nJ(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function n0({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=nZ++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ip.Q.value&&(nK.nodes=nK.calculatedTargetDeltas=nK.calculatedProjections=0),this.nodes.forEach(n2),this.nodes.forEach(rt),this.nodes.forEach(re),this.nodes.forEach(n3),ip.Q.addProjectionMetrics&&ip.Q.addProjectionMetrics(nK)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nA)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tw.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,nP.x)(e)&&(!(0,nP.x)(e)||"svg"!==e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i;let n=0,r=()=>this.root.updateBlockedByResize=!1;G.Gt.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tm.k.now(),n=({timestamp:e})=>{let r=e-i;r>=250&&((0,G.WG)(n),t(r-250))};return G.Gt.setup(n,!0),()=>(0,G.WG)(n)}(r,250),nm.hasAnimatedSinceResize&&(nm.hasAnimatedSinceResize=!1,this.nodes.forEach(n7)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||ra,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!nq(this.targetLayout,n),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...eb(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||n7(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,G.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ri),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[eg];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",G.Gt,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n6);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(n9);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(n8),this.nodes.forEach(n1),this.nodes.forEach(n5)):this.nodes.forEach(n9),this.clearAllSnapshots();let t=tm.k.now();G.uv.delta=(0,eE.q)(0,1e3/60,t-G.uv.timestamp),G.uv.timestamp=t,G.uv.isProcessing=!0,G.PP.update.process(G.uv),G.PP.preRender.process(G.uv),G.PP.render.process(G.uv),G.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tx.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n4),this.sharedNodes.forEach(rn)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,G.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){G.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||i0(this.snapshot.measuredBox.x)||i0(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tM(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nz(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||P(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),rh((e=n).x),rh(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return tM();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rc))){let{scroll:t}=this.root;t&&(E(e.x,t.offset.x),E(e.y,t.offset.y))}return e}removeElementScroll(t){let e=tM();if(nO(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nO(e,t),E(e.x,r.offset.x),E(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=tM();nO(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&k(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),P(n.latestValues)&&k(i,n.latestValues)}return P(this.latestValues)&&k(i,this.latestValues),i}removeTransform(t){let e=tM();nO(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!P(i.latestValues))continue;b(i.latestValues)&&i.updateSnapshot();let n=tM();nO(n,i.measurePageBox()),nU(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return P(this.latestValues)&&nU(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==G.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=G.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tM(),this.relativeTargetOrigin=tM(),i4(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=tM(),this.targetWithTransforms=tM()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,i2(s.x,o.x,a.x),i2(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nO(this.target,this.layout.layoutBox),M(this.target,this.targetDelta)):nO(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tM(),this.relativeTargetOrigin=tM(),i4(this.relativeTargetOrigin,this.target,t.target),nO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ip.Q.value&&nK.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||b(this.parent.latestValues)||T(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===G.uv.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nO(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&k(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,M(t,s)),n&&P(r.latestValues)&&k(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=tM());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nB(this.prevProjectionDelta.x,this.projectionDelta.x),nB(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),i5(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nX(this.projectionDelta.x,this.prevProjectionDelta.x)&&nX(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ip.Q.value&&nK.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tS(),this.projectionDelta=tS(),this.projectionDeltaWithTransform=tS()}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=tS();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=tM(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(ro));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(rr(o.x,t.x,n),rr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,f;i4(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,rs(p.x,f.x,a.x,n),rs(p.y,f.y,a.y,n),i&&(u=this.relativeTarget,c=i,nG(u.x,c.x)&&nG(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=tM()),nO(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=(0,x.k)(0,i.opacity??1,nj(n)),t.opacityExit=(0,x.k)(e.opacity??1,0,nD(n))):s&&(t.opacity=(0,x.k)(e.opacity??1,i.opacity??1,n));for(let r=0;r<nE;r++){let s=`border${nM[r]}Radius`,o=nV(e,s),a=nV(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nk(o)===nk(a)?(t[s]=Math.max((0,x.k)(nC(o),nC(a),n),0),(R.KN.test(a)||R.KN.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,x.k)(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,G.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=G.Gt.update(()=>{nm.hasAnimatedSinceResize=!0,eV.layout++,this.motionValue||(this.motionValue=(0,tg.OQ)(0)),this.currentAnimation=function(t,e,i){let n=tf(t)?t:(0,tg.OQ)(t);return n.start(iL("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{eV.layout--},onComplete:()=>{eV.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&rd(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||tM();let e=i0(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=i0(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nO(e,i),k(e,r),i5(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nY),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nJ("z",t,n,this.animationValues);for(let e=0;e<nQ.length;e++)nJ(`rotate${nQ[e]}`,t,n,this.animationValues),nJ(`skew${nQ[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eh(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eh(e?.pointerEvents)||""),this.hasProjected&&!P(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,tq){if(void 0===r[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=tq[e],l="none"===s?r[e]:i(r[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?eh(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(n6),this.root.sharedNodes.clear()}}}function n1(t){t.updateLayout()}function n5(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?i6(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=i0(n);n.min=i[t].min,n.max=n.min+r}):rd(r,e.layoutBox,i)&&i6(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=i0(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=tS();i5(o,i,e.layoutBox);let a=tS();s?i5(a,t.applyTransform(n,!0),e.measuredBox):i5(a,i,e.layoutBox);let l=!nz(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=tM();i4(o,e.layoutBox,r.layoutBox);let a=tM();i4(a,i,s.layoutBox),nq(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n2(t){ip.Q.value&&nK.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function n3(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n4(t){t.clearSnapshot()}function n6(t){t.clearMeasurements()}function n9(t){t.isLayoutDirty=!1}function n8(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n7(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function rt(t){t.resolveTargetDelta()}function re(t){t.calcProjection()}function ri(t){t.resetSkewAndRotation()}function rn(t){t.removeLeadSnapshot()}function rr(t,e,i){t.translate=(0,x.k)(e.translate,0,i),t.scale=(0,x.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function rs(t,e,i,n){t.min=(0,x.k)(e.min,i.min,n),t.max=(0,x.k)(e.max,i.max,n)}function ro(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ra={duration:.45,ease:[.4,0,.1,1]},rl=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ru=rl("applewebkit/")&&!rl("chrome/")?Math.round:eG.l;function rh(t){t.min=ru(t.min),t.max=ru(t.max)}function rd(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nH(e)-nH(i)))}function rc(t){return t!==t.root&&t.scroll?.wasRoot}let rp=n0({attachResizeListener:(t,e)=>iY(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rf={current:void 0},rm=n0({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rf.current){let t=new rp({});t.mount(window),t.setOptions({layoutScroll:!0}),rf.current=t}return rf.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var rg=i(4999);function rv(t,e){let i=(0,rg.K)(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function ry(t){return!("touch"===t.pointerType||iX.x||iX.y)}function rx(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&G.Gt.postRender(()=>r(e,iQ(e)))}class rw extends iG{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=rv(t,i),o=t=>{if(!ry(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{ry(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(rx(this.node,e,"Start"),t=>rx(this.node,t,"End"))))}unmount(){}}class rb extends iG{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,eM.F)(iY(this.node.current,"focus",()=>this.onFocus()),iY(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var rP=i(2804);let rT=(t,e)=>!!e&&(t===e||rT(t,e.parentElement)),rS=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rA=new WeakSet;function rM(t){return e=>{"Enter"===e.key&&t(e)}}function rE(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rC=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rM(()=>{if(rA.has(i))return;rE(i,"down");let t=rM(()=>{rE(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rE(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rk(t){return iK(t)&&!(iX.x||iX.y)}function rV(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&G.Gt.postRender(()=>r(e,iQ(e)))}class rj extends iG{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=rv(t,i),o=t=>{let n=t.currentTarget;if(!rk(t))return;rA.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rA.has(n)&&rA.delete(n),rk(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rT(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,rP.s)(t)&&(t.addEventListener("focus",t=>rC(t,r)),rS.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rV(this.node,e,"Start"),(t,{success:e})=>rV(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rD=new WeakMap,rR=new WeakMap,rL=t=>{let e=rD.get(t.target);e&&e(t)},rO=t=>{t.forEach(rL)},rB={some:0,all:1};class rF extends iG{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rB[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rR.has(i)||rR.set(i,{});let n=rR.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rO,{root:t,...e})),n[r]}(e);return rD.set(t,i),n.observe(t),()=>{rD.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rI=function(t,e){if("undefined"==typeof Proxy)return ex;let i=new Map,n=(i,n)=>ex(i,n,t,e);return new Proxy((t,e)=>n(t,e),{get:(r,s)=>"create"===s?n:(i.has(s)||i.set(s,ex(s,void 0,t,e)),i.get(s))})}({animation:{Feature:i$},exit:{Feature:iH},inView:{Feature:rF},tap:{Feature:rj},focus:{Feature:rb},hover:{Feature:rw},pan:{Feature:nf},drag:{Feature:nc,ProjectionNode:rm,MeasureLayout:nw},layout:{ProjectionNode:rm,MeasureLayout:nw}},(t,e)=>t4(t)?new t2(e):new tY(e,{allowProjection:t!==n.Fragment}))},5687:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},9234:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(2115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},5403:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(2115);let r=i(5687).B?n.useLayoutEffect:n.useEffect},5580:(t,e,i)=>{let n,r;i.d(e,{L:()=>H});var s=i(9421),o=i(5107),a=i(2115),l=i(6054),u=i(3932);function h(t,e){let i;let n=()=>{let{currentTime:n}=e,r=(null===n?0:n.value)/100;i!==r&&t(r),i=r};return u.Gt.preUpdate(n,!0),()=>(0,u.WG)(n)}var d=i(5425),c=i(8091),p=i(4999);let f=new WeakMap,m=(t,e,i)=>(n,r)=>r&&r[0]?r[0][t+"Size"]:(0,c.x)(n)&&"getBBox"in n?n.getBBox()[e]:n[i],g=m("inline","width","offsetWidth"),v=m("block","height","offsetHeight");function y({target:t,borderBoxSize:e}){f.get(t)?.forEach(i=>{i(t,{get width(){return g(t,e)},get height(){return v(t,e)}})})}function x(t){t.forEach(y)}let w=new Set;var b=i(9615),P=i(3700);let T=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),S=()=>({time:0,x:T(),y:T()}),A={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function M(t,e,i,n){let r=i[e],{length:s,position:o}=A[e],a=r.current,l=i.time;r.current=t[`scroll${o}`],r.scrollLength=t[`scroll${s}`]-t[`client${s}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=(0,b.q)(0,r.scrollLength,r.current);let u=n-l;r.velocity=u>50?0:(0,P.f)(r.current-a,u)}var E=i(1136),C=i(6421),k=i(5821),V=i(2804);let j={start:0,center:.5,end:1};function D(t,e,i=0){let n=0;if(t in j&&(t=j[t]),"string"==typeof t){let e=parseFloat(t);t.endsWith("px")?n=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?n=e/100*document.documentElement.clientWidth:t.endsWith("vh")?n=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(n=e*t),i+n}let R=[0,0],L={All:[[0,0],[1,1]]},O={x:0,y:0},B=new WeakMap,F=new WeakMap,I=new WeakMap,W=t=>t===document.scrollingElement?window:t;function _(t,{container:e=document.scrollingElement,...i}={}){if(!e)return l.l;let s=I.get(e);s||(s=new Set,I.set(e,s));let o=function(t,e,i,n={}){return{measure:e=>{!function(t,e=t,i){if(i.x.targetOffset=0,i.y.targetOffset=0,e!==t){let n=e;for(;n&&n!==t;)i.x.targetOffset+=n.offsetLeft,i.y.targetOffset+=n.offsetTop,n=n.offsetParent}i.x.targetLength=e===t?e.scrollWidth:e.clientWidth,i.y.targetLength=e===t?e.scrollHeight:e.clientHeight,i.x.containerLength=t.clientWidth,i.y.containerLength=t.clientHeight}(t,n.target,i),M(t,"x",i,e),M(t,"y",i,e),i.time=e,(n.offset||n.target)&&function(t,e,i){let{offset:n=L.All}=i,{target:r=t,axis:s="y"}=i,o="y"===s?"height":"width",a=r!==t?function(t,e){let i={x:0,y:0},n=t;for(;n&&n!==e;)if((0,V.s)(n))i.x+=n.offsetLeft,i.y+=n.offsetTop,n=n.offsetParent;else if("svg"===n.tagName){let t=n.getBoundingClientRect(),e=(n=n.parentElement).getBoundingClientRect();i.x+=t.left-e.left,i.y+=t.top-e.top}else if(n instanceof SVGGraphicsElement){let{x:t,y:e}=n.getBBox();i.x+=t,i.y+=e;let r=null,s=n.parentNode;for(;!r;)"svg"===s.tagName&&(r=s),s=n.parentNode;n=r}else break;return i}(r,t):O,l=r===t?{width:t.scrollWidth,height:t.scrollHeight}:"getBBox"in r&&"svg"!==r.tagName?r.getBBox():{width:r.clientWidth,height:r.clientHeight},u={width:t.clientWidth,height:t.clientHeight};e[s].offset.length=0;let h=!e[s].interpolate,d=n.length;for(let t=0;t<d;t++){let i=function(t,e,i,n){let r=Array.isArray(t)?t:R,s=0;return"number"==typeof t?r=[t,t]:"string"==typeof t&&(r=(t=t.trim()).includes(" ")?t.split(" "):[t,j[t]?t:"0"]),D(r[0],i,n)-D(r[1],e)}(n[t],u[o],l[o],a[s]);h||i===e[s].interpolatorOffsets[t]||(h=!0),e[s].offset[t]=i}h&&(e[s].interpolate=(0,E.G)(e[s].offset,(0,C.Z)(n),{clamp:!1}),e[s].interpolatorOffsets=[...e[s].offset]),e[s].progress=(0,k.q)(0,1,e[s].interpolate(e[s].current))}(t,i,n)},notify:()=>e(i)}}(e,t,S(),i);if(s.add(o),!B.has(e)){let t=()=>{for(let t of s)t.measure(u.uv.timestamp);u.Gt.preUpdate(i)},i=()=>{for(let t of s)t.notify()},o=()=>u.Gt.read(t);B.set(e,o);let a=W(e);window.addEventListener("resize",o,{passive:!0}),e!==document.documentElement&&F.set(e,"function"==typeof e?(w.add(e),r||(r=()=>{let t={get width(){return window.innerWidth},get height(){return window.innerHeight}};w.forEach(e=>e(t))},window.addEventListener("resize",r)),()=>{w.delete(e),w.size||"function"!=typeof r||(window.removeEventListener("resize",r),r=void 0)}):function(t,e){n||"undefined"==typeof ResizeObserver||(n=new ResizeObserver(x));let i=(0,p.K)(t);return i.forEach(t=>{let i=f.get(t);i||(i=new Set,f.set(t,i)),i.add(e),n?.observe(t)}),()=>{i.forEach(t=>{let i=f.get(t);i?.delete(e),i?.size||n?.unobserve(t)})}}(e,o)),a.addEventListener("scroll",o,{passive:!0}),o()}let a=B.get(e);return u.Gt.read(a,!1,!0),()=>{(0,u.WG)(a);let t=I.get(e);if(!t||(t.delete(o),t.size))return;let i=B.get(e);B.delete(e),i&&(W(e).removeEventListener("scroll",i),F.get(e)?.(),window.removeEventListener("resize",i))}}let U=new Map;function N({source:t,container:e,...i}){let{axis:n}=i;t&&(e=t);let r=U.get(e)??new Map;U.set(e,r);let s=i.target??"self",o=r.get(s)??{},a=n+(i.offset??[]).join(",");return o[a]||(o[a]=!i.target&&(0,d.J)()?new ScrollTimeline({source:e,axis:n}):function(t){let e={value:0},i=_(i=>{e.value=100*i[t.axis].progress},t);return{currentTime:e,cancel:i}}({container:e,...i})),o[a]}var z=i(9234),G=i(5403);let $=()=>({scrollX:(0,s.OQ)(0),scrollY:(0,s.OQ)(0),scrollXProgress:(0,s.OQ)(0),scrollYProgress:(0,s.OQ)(0)}),q=t=>!!t&&!t.current;function H({container:t,target:e,...i}={}){let n=(0,z.M)($),r=(0,a.useRef)(null),s=(0,a.useRef)(!1),u=(0,a.useCallback)(()=>(r.current=function(t,{axis:e="y",container:i=document.scrollingElement,...n}={}){if(!i)return l.l;let r={axis:e,container:i,...n};return"function"==typeof t?2===t.length?_(e=>{t(e[r.axis].progress,e)},r):h(t,N(r)):function(t,e){let i=N(e);return t.attachTimeline({timeline:e.target?void 0:i,observe:t=>(t.pause(),h(e=>{t.time=t.duration*e},i))})}(t,r)}((t,{x:e,y:i})=>{n.scrollX.set(e.current),n.scrollXProgress.set(e.progress),n.scrollY.set(i.current),n.scrollYProgress.set(i.progress)},{...i,container:t?.current||void 0,target:e?.current||void 0}),()=>{r.current?.()}),[t,e,JSON.stringify(i.offset)]);return(0,G.E)(()=>{if(s.current=!1,!(q(t)||q(e)))return u();s.current=!0},[u]),(0,a.useEffect)(()=>s.current?((0,o.V)(!q(t),"Container ref is defined but not hydrated","use-scroll-ref"),(0,o.V)(!q(e),"Target ref is defined but not hydrated","use-scroll-ref"),u()):void 0,[u]),n}},2812:(t,e,i)=>{i.d(e,{G:()=>d});var n=i(1136),r=i(9234),s=i(3932),o=i(5403),a=i(9421),l=i(2115),u=i(7249);function h(t,e){let i=function(t){let e=(0,r.M)(()=>(0,a.OQ)(t)),{isStatic:i}=(0,l.useContext)(u.Q);if(i){let[,i]=(0,l.useState)(t);(0,l.useEffect)(()=>e.on("change",i),[])}return e}(e()),n=()=>i.set(e());return n(),(0,o.E)(()=>{let e=()=>s.Gt.preRender(n,!1,!0),i=t.map(t=>t.on("change",e));return()=>{i.forEach(t=>t()),(0,s.WG)(n)}}),i}function d(t,e,i,r){if("function"==typeof t)return function(t){a.bt.current=[],t();let e=h(a.bt.current,t);return a.bt.current=void 0,e}(t);let s="function"==typeof e?e:function(...t){let e=!Array.isArray(t[0]),i=e?0:-1,r=t[0+i],s=t[1+i],o=t[2+i],a=t[3+i],l=(0,n.G)(s,o,a);return e?l(r):l}(e,i,r);return Array.isArray(t)?c(t,s):c([t],([t])=>s(t))}function c(t,e){let i=(0,r.M)(()=>[]);return h(t,()=>{i.length=0;let n=t.length;for(let e=0;e<n;e++)i[e]=t[e].get();return e(i)})}},6421:(t,e,i)=>{i.d(e,{Z:()=>s});var n=i(9615),r=i(1607);function s(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let o=(0,n.q)(0,e,s);t.push((0,r.k)(i,1,o))}}(e,t.length-1),e}},7309:(t,e,i)=>{i.d(e,{j:()=>r,p:()=>o});let n=t=>e=>"string"==typeof e&&e.startsWith(t),r=n("--"),s=n("var(--"),o=t=>!!s(t)&&a.test(t.split("/*")[0].trim()),a=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},506:(t,e,i)=>{i.d(e,{I:()=>o});var n=i(4148);let r=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var s=i(5853);function o(t,e){let i=!1,o=!0,a={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=r.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,o=!1,a=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){a.has(e)&&(d.schedule(e),t()),u++,e(l)}let d={schedule:(t,e=!1,s=!1)=>{let o=s&&r?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(l=t,r){o=!0;return}r=!0,[i,n]=[n,i],i.forEach(h),e&&s.Q.value&&s.Q.value.frameloop[e].push(u),u=0,i.clear(),r=!1,o&&(o=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:h,read:d,resolveKeyframes:c,preUpdate:p,update:f,preRender:m,render:g,postRender:v}=u,y=()=>{let r=n.W.useManualTiming?a.timestamp:performance.now();i=!1,n.W.useManualTiming||(a.delta=o?1e3/60:Math.max(Math.min(r-a.timestamp,40),1)),a.timestamp=r,a.isProcessing=!0,h.process(a),d.process(a),c.process(a),p.process(a),f.process(a),m.process(a),g.process(a),v.process(a),a.isProcessing=!1,i&&e&&(o=!1,t(y))},x=()=>{i=!0,o=!0,a.isProcessing||t(y)};return{schedule:r.reduce((t,e)=>{let n=u[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<r.length;e++)u[r[e]].cancel(t)},state:a,steps:u}}},3932:(t,e,i)=>{i.d(e,{Gt:()=>r,PP:()=>a,WG:()=>s,uv:()=>o});var n=i(6054);let{schedule:r,cancel:s,state:o,steps:a}=(0,i(506).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},5850:(t,e,i)=>{let n;i.d(e,{k:()=>a});var r=i(4148),s=i(3932);function o(){n=void 0}let a={now:()=>(void 0===n&&a.set(s.uv.isProcessing||r.W.useManualTiming?s.uv.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(o)}}},5853:(t,e,i)=>{i.d(e,{Q:()=>n});let n={value:null,addProjectionMetrics:null}},1136:(t,e,i)=>{i.d(e,{G:()=>h});var n=i(4148),r=i(6054),s=i(1046),o=i(5107),a=i(9615),l=i(5821),u=i(9674);function h(t,e,{clamp:i=!0,ease:d,mixer:c}={}){let p=t.length;if((0,o.V)(p===e.length,"Both input and output ranges must be the same length","range-length"),1===p)return()=>e[0];if(2===p&&e[0]===e[1])return()=>e[1];let f=t[0]===t[1];t[0]>t[p-1]&&(t=[...t].reverse(),e=[...e].reverse());let m=function(t,e,i){let o=[],a=i||n.W.mix||u.j,l=t.length-1;for(let i=0;i<l;i++){let n=a(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||r.l:e;n=(0,s.F)(t,n)}o.push(n)}return o}(e,d,c),g=m.length,v=i=>{if(f&&i<t[0])return e[0];let n=0;if(g>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=(0,a.q)(t[n],t[n+1],i);return m[n](r)};return i?e=>v((0,l.q)(t[0],t[p-1],e)):v}},2804:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(8180);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},8091:(t,e,i)=>{i.d(e,{x:()=>r});var n=i(8180);function r(t){return(0,n.G)(t)&&"ownerSVGElement"in t}},9674:(t,e,i)=>{i.d(e,{j:()=>A});var n=i(1046),r=i(5107),s=i(7309),o=i(8207),a=i(663),l=i(4964),u=i(1965);function h(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var d=i(4441);function c(t,e){return i=>i>0?e:t}var p=i(1607);let f=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},m=[l.u,d.B,u.V],g=t=>m.find(e=>e.test(t));function v(t){let e=g(t);if((0,r.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===u.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=h(a,n,t+1/3),s=h(a,n,t),o=h(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let y=(t,e)=>{let i=v(t),n=v(e);if(!i||!n)return c(t,e);let r={...i};return t=>(r.red=f(i.red,n.red,t),r.green=f(i.green,n.green,t),r.blue=f(i.blue,n.blue,t),r.alpha=(0,p.k)(i.alpha,n.alpha,t),d.B.transform(r))},x=new Set(["none","hidden"]);function w(t,e){return i=>(0,p.k)(t,e,i)}function b(t){return"number"==typeof t?w:"string"==typeof t?(0,s.p)(t)?c:o.y.test(t)?y:S:Array.isArray(t)?P:"object"==typeof t?o.y.test(t)?y:T:c}function P(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>b(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function T(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=b(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let S=(t,e)=>{let i=a.f.createTransformer(e),s=(0,a.V)(t),o=(0,a.V)(e);return s.indexes.var.length===o.indexes.var.length&&s.indexes.color.length===o.indexes.color.length&&s.indexes.number.length>=o.indexes.number.length?x.has(t)&&!o.values.length||x.has(e)&&!s.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,n.F)(P(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(s,o),o.values),i):((0,r.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),c(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,p.k)(t,e,i):b(t)(t,e)}},1607:(t,e,i)=>{i.d(e,{k:()=>n});let n=(t,e,i)=>t+(e-t)*i},4999:(t,e,i)=>{i.d(e,{K:()=>n});function n(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);let r=i?.[t]??n.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}},5425:(t,e,i)=>{i.d(e,{J:()=>n});let n=(0,i(4996).p)(()=>void 0!==window.ScrollTimeline)},9421:(t,e,i)=>{i.d(e,{OQ:()=>h,bt:()=>l});var n=i(2539),r=i(3700),s=i(5850),o=i(3932);let a=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=s.k.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),o.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,r.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},4964:(t,e,i)=>{i.d(e,{u:()=>r});var n=i(4441);let r={test:(0,i(8196).$)("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:n.B.transform}},1965:(t,e,i)=>{i.d(e,{V:()=>a});var n=i(2264),r=i(3137),s=i(8480),o=i(8196);let a={test:(0,o.$)("hsl","hue"),parse:(0,o.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:o=1})=>"hsla("+Math.round(t)+", "+r.KN.transform((0,s.a)(e))+", "+r.KN.transform((0,s.a)(i))+", "+(0,s.a)(n.X4.transform(o))+")"}},8207:(t,e,i)=>{i.d(e,{y:()=>o});var n=i(4964),r=i(1965),s=i(4441);let o={test:t=>s.B.test(t)||n.u.test(t)||r.V.test(t),parse:t=>s.B.test(t)?s.B.parse(t):r.V.test(t)?r.V.parse(t):n.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?s.B.transform(t):r.V.transform(t),getAnimatableNone:t=>{let e=o.parse(t);return e.alpha=0,o.transform(e)}}},4441:(t,e,i)=>{i.d(e,{B:()=>u});var n=i(5821),r=i(2264),s=i(8480),o=i(8196);let a=t=>(0,n.q)(0,255,t),l={...r.ai,transform:t=>Math.round(a(t))},u={test:(0,o.$)("rgb","red"),parse:(0,o.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,s.a)(r.X4.transform(n))+")"}},8196:(t,e,i)=>{i.d(e,{$:()=>s,q:()=>o});var n=i(4885);let r=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,s=(t,e)=>i=>!!("string"==typeof i&&r.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),o=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[s,o,a,l]=r.match(n.S);return{[t]:parseFloat(s),[e]:parseFloat(o),[i]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},663:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>f});var n=i(8207);let r=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var s=i(4885),o=i(8480);let a="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},s=[],o=0,h=e.replace(u,t=>(n.y.test(t)?(r.color.push(o),s.push(l),i.push(n.y.parse(t))):t.startsWith("var(")?(r.var.push(o),s.push("var"),i.push(t)):(r.number.push(o),s.push(a),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:h,indexes:r,types:s}}function d(t){return h(t).values}function c(t){let{split:e,types:i}=h(t),r=e.length;return t=>{let s="";for(let u=0;u<r;u++)if(s+=e[u],void 0!==t[u]){let e=i[u];e===a?s+=(0,o.a)(t[u]):e===l?s+=n.y.transform(t[u]):s+=t[u]}return s}}let p=t=>"number"==typeof t?0:n.y.test(t)?n.y.getAnimatableNone(t):t,f={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(s.S)?.length||0)+(t.match(r)?.length||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},2264:(t,e,i)=>{i.d(e,{X4:()=>s,ai:()=>r,hs:()=>o});var n=i(5821);let r={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},s={...r,transform:t=>(0,n.q)(0,1,t)},o={...r,default:1}},3137:(t,e,i)=>{i.d(e,{KN:()=>s,gQ:()=>u,px:()=>o,uj:()=>r,vh:()=>a,vw:()=>l});let n=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),r=n("deg"),s=n("%"),o=n("px"),a=n("vh"),l=n("vw"),u={...s,parse:t=>s.parse(t)/100,transform:t=>s.transform(100*t)}},4885:(t,e,i)=>{i.d(e,{S:()=>n});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},8480:(t,e,i)=>{i.d(e,{a:()=>n});let n=t=>Math.round(1e5*t)/1e5},1555:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function r(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>r,Kq:()=>n})},5821:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>i>e?e:i<t?t:i},5107:(t,e,i)=>{i.d(e,{$:()=>n,V:()=>r});let n=()=>{},r=()=>{}},4148:(t,e,i)=>{i.d(e,{W:()=>n});let n={}},8180:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},4996:(t,e,i)=>{i.d(e,{p:()=>n});function n(t){let e;return()=>(void 0===e&&(e=t()),e)}},6054:(t,e,i)=>{i.d(e,{l:()=>n});let n=t=>t},1046:(t,e,i)=>{i.d(e,{F:()=>r});let n=(t,e)=>i=>e(t(i)),r=(...t)=>t.reduce(n)},9615:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n}},2539:(t,e,i)=>{i.d(e,{v:()=>r});var n=i(1555);class r{constructor(){this.subscriptions=[]}add(t){return(0,n.Kq)(this.subscriptions,t),()=>(0,n.Ai)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},3700:(t,e,i)=>{i.d(e,{f:()=>n});function n(t,e){return e?1e3/e*t:0}}}]);