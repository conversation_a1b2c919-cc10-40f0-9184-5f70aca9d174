"use client";

import { motion } from "framer-motion";
import { useState } from "react";

const skills = [
  "JavaScript", "TypeScript", "React", "Next.js", "Node.js", "Express.js",
  "MongoDB", "PostgreSQL", "AWS", "Docker", "Kubernetes", "Redis",
  "GraphQL", "REST APIs", "Python", "Java", "Angular", "Vue.js",
  "Tailwind CSS", "SASS", "Git", "Jenkins", "Terraform", "Microservices",
  "Socket.io", "Kafka", "Elasticsearch", "NATS", "Firebase", "Vercel"
];

// Duplicate skills for seamless infinite scroll
const duplicatedSkills = [...skills, ...skills];

export default function SkillsSection() {
  const [isPaused, setIsPaused] = useState(false);

  return (
    <section className="py-20 px-4 overflow-hidden">
      <div className="max-w-6xl mx-auto">
        <motion.h2 
          initial={{ opacity: 0, y: 20 }} 
          whileInView={{ opacity: 1, y: 0 }} 
          viewport={{ once: true }}
          className="text-3xl font-bold mb-16 text-center"
        >
          Skills & Technologies
        </motion.h2>

        <div className="relative">
          {/* Gradient overlays for fade effect */}
          <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-gray-950 to-transparent z-10 pointer-events-none" />
          <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-gray-950 to-transparent z-10 pointer-events-none" />
          
          {/* Scrolling container */}
          <div 
            className="flex gap-6 w-fit"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
          >
            <motion.div
              className="flex gap-6"
              animate={{
                x: isPaused ? 0 : [-1920, 0]
              }}
              transition={{
                x: {
                  repeat: Infinity,
                  repeatType: "loop",
                  duration: 40,
                  ease: "linear"
                }
              }}
              style={{
                animationPlayState: isPaused ? 'paused' : 'running'
              }}
            >
              {duplicatedSkills.map((skill, index) => (
                <SkillCard key={`${skill}-${index}`} skill={skill} />
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}

interface SkillCardProps {
  skill: string;
}

function SkillCard({ skill }: SkillCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="relative group cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
    >
      {/* Gradient border on hover */}
      <div 
        className={`absolute inset-0 rounded-xl transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}
        style={{
          background: 'linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8))',
          padding: '2px'
        }}
      >
        <div className="w-full h-full bg-gray-900/90 rounded-xl" />
      </div>

      {/* Glass effect card */}
      <div className="relative px-6 py-4 min-w-[140px] text-center">
        {/* Glass background */}
        <div className="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10" />
        
        {/* Content */}
        <div className="relative z-10">
          <span className="text-sm font-medium text-gray-200 whitespace-nowrap">
            {skill}
          </span>
        </div>

        {/* Hover glow effect */}
        <div 
          className={`absolute inset-0 rounded-xl transition-opacity duration-300 ${
            isHovered ? 'opacity-20' : 'opacity-0'
          }`}
          style={{
            background: 'radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)'
          }}
        />
      </div>
    </motion.div>
  );
}
