<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/499f96a3048ebeb3.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-2fdd60e345297184.js"/><script src="/_next/static/chunks/4bd1b696-49dfdb1645950f8a.js" async=""></script><script src="/_next/static/chunks/517-1b72e8afd3a28b6b.js" async=""></script><script src="/_next/static/chunks/main-app-bfd5063d7ed1810d.js" async=""></script><script src="/_next/static/chunks/704-c1b7454acf2285d3.js" async=""></script><script src="/_next/static/chunks/app/page-a4cc83484493c8d6.js" async=""></script><title>[Ben Basil Tomy] - Software Engineer</title><meta name="description" content="Welcome to my portfolio! I am a passionate full-stack developer who bridges the gap between frontend and backend development. Specializing in creating complete web solutions, from beautiful user interfaces to robust server architectures."/><meta name="author" content="[Ben Basil Tomy]"/><meta name="keywords" content="Full-Stack Developer,Software Engineer,Frontend Development,Backend Development,React,Next.js,Node.js,TypeScript,Database Design,API Development,Cloud Solutions,DevOps,System Architecture,Web Development,[Ben Basil Tomy]"/><meta name="creator" content="[Ben Basil Tomy]"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta property="og:title" content="[Ben Basil Tomy] - Full-Stack Developer Portfolio"/><meta property="og:description" content="Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack."/><meta property="og:url" content="https://your-domain.com"/><meta property="og:site_name" content="[Ben Basil Tomy] - Portfolio"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="http://localhost:3000/og-image.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="[Ben Basil Tomy] - Full-Stack Developer Portfolio"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@yourusername"/><meta name="twitter:title" content="[Ben Basil Tomy] - Full-Stack Developer"/><meta name="twitter:description" content="Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack."/><meta name="twitter:image" content="http://localhost:3000/og-image.jpg"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><main class="min-h-screen bg-gradient-to-b from-gray-950 to-black text-white"><section class="min-h-screen relative overflow-hidden py-24 md:py-0"><div class="absolute inset-0"><div class="absolute inset-0 bg-[url(&#x27;/grid.svg&#x27;)] opacity-10"></div><div class="absolute inset-0 pointer-events-none transition-opacity duration-300" style="background:radial-gradient(600px circle at 0px 0px,
              rgba(20, 184, 166, 0.25) 0%,
              rgba(20, 184, 166, 0.15) 25%,
              rgba(20, 184, 166, 0.08) 50%,
              transparent 70%)"></div></div><div class="relative z-10 min-h-screen flex flex-col items-center justify-center px-4 pt-8 md:pt-0"><div class="text-center mb-8 md:mb-12" style="opacity:0;transform:translateY(20px)"><div class="space-y-3 md:space-y-4 mb-6 md:mb-8"><h1 class="text-4xl md:text-7xl font-bold text-transparent bg-clip-text text-white">Ben Basil Tomy</h1><h2 class="text-2xl md:text-4xl font-bold text-gray-300">Software Engineer</h2><p class="text-lg md:text-xl text-gray-400 max-w-2xl mx-auto">I craft end-to-end solutions with 2+ years of experience building scalable applications.</p></div></div><div class="w-full max-w-5xl mx-auto relative px-2 md:px-4"><div class="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800 p-4 md:p-8" style="opacity:0"><div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8"><div class="p-4 md:p-6 rounded-lg transition-colors border-2 bg-gray-800/50 hover:bg-gray-800/80 border-transparent"><h3 class="text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400">Frontend Development</h3><ul class="space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400"><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>Modern Angular Architectures</li><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>Performance Optimization</li><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>Performance Optimization</li></ul></div><div class="p-4 md:p-6 rounded-lg transition-colors border-2 bg-gray-800/50 hover:bg-gray-800/80 border-transparent"><h3 class="text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400">Backend Development</h3><ul class="space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400"><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>API Design &amp; Development</li><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>Database Architecture</li><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>Real-time Systems</li></ul></div><div class="p-4 md:p-6 rounded-lg transition-colors border-2 bg-gray-800/50 hover:bg-gray-800/80 border-transparent"><h3 class="text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400">DevOps &amp; Cloud</h3><ul class="space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400"><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>AWS Infrastructure</li><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>Docker</li><li class="flex items-center gap-2"><div class="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>Scalable Architecture</li></ul></div></div></div></div></div></section><section class="py-20 px-4"><div class="max-w-6xl mx-auto"><div class="mb-16"></div><div class="relative overflow-hidden"><div class="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-gray-950 to-transparent z-10 pointer-events-none"></div><div class="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-gray-950 to-transparent z-10 pointer-events-none"></div><div class="overflow-hidden"><div class="flex gap-6 w-fit"><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">JavaScript</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">TypeScript</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Node.js</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Express.js</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">MongoDB</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">AWS</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Docker</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Redis</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">REST APIs</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Angular</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">SASS</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Git</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Microservices</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">NATS</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">MQTT</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">JavaScript</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">TypeScript</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Node.js</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Express.js</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">MongoDB</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">AWS</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Docker</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Redis</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">REST APIs</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Angular</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">SASS</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Git</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">Microservices</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">NATS</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div><div class="relative group cursor-pointer"><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8));padding:2px"><div class="w-full h-full bg-gray-900/90 rounded-xl"></div></div><div class="relative px-6 py-4 min-w-[140px] text-center"><div class="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"></div><div class="relative z-10"><span class="text-sm font-medium text-gray-200 whitespace-nowrap">MQTT</span></div><div class="absolute inset-0 rounded-xl transition-opacity duration-300 opacity-0" style="background:radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"></div></div></div></div></div></div></div></section><section class="py-20 px-4"><div class="max-w-6xl mx-auto"><h2 class="text-3xl font-bold mb-16 text-center" style="opacity:0;transform:translateY(20px)">Experience</h2><div class="space-y-16"><div class="relative bg-gray-900/95 backdrop-blur-md rounded-xl overflow-hidden border border-white/10 shadow-2xl transition-all duration-300" style="transform-style:preserve-3d;opacity:0;transform:perspective(1000px) translateY(20px) rotateX(15deg)"><div class="absolute inset-0 rounded-xl bg-gradient-to-br from-white/8 via-transparent to-gray-900/30 pointer-events-none"></div><div class="absolute inset-0 rounded-xl shadow-inner pointer-events-none" style="box-shadow:inset 0 1px 0 0 rgba(255, 255, 255, 0.08), inset 0 -1px 0 0 rgba(0, 0, 0, 0.4)"></div><div class="relative p-4 md:p-6 rounded-lg transition-all duration-300 bg-white/5 backdrop-blur-sm hover:bg-white/8"><div class="relative z-10 p-8"><div class="grid grid-cols-1 lg:grid-cols-2 gap-8"><div class="space-y-6"><div><h3 class="text-2xl font-bold mb-4">Cavli Wireless</h3><p class="text-gray-400">A high-performance e-commerce solution handling 100K+ daily transactions with real-time inventory and ML-powered recommendations.</p></div><div class="grid grid-cols-2 gap-6"><div><h4 class="text-sm font-semibold text-teal-400 mb-3">Frontend Architecture</h4><ul class="space-y-2 text-sm text-gray-400"><li>• Next.js Server Components</li><li>• Real-time Cart &amp; Inventory</li><li>• Stripe Payment Integration</li><li>• PWA with Offline Support</li></ul></div><div><h4 class="text-sm font-semibold text-teal-400 mb-3">Backend Systems</h4><ul class="space-y-2 text-sm text-gray-400"><li>• Node.js Microservices</li><li>• Redis Caching Layer</li><li>• Kafka Event Streaming</li><li>• Elasticsearch Product Search</li></ul></div></div><div class="space-y-3"><h4 class="text-sm font-semibold text-teal-400">Key Achievements</h4><ul class="space-y-2 text-sm text-gray-400"><li>• 99.99% Uptime with Blue-Green Deployment</li><li>• 300ms Average API Response Time</li><li>• 45% Reduction in Infrastructure Costs</li></ul></div></div><div class="bg-black/30 rounded-xl p-6"><img alt="A description of the image" loading="lazy" width="800" height="300" decoding="async" data-nimg="1" style="color:transparent" srcSet="/_next/image?url=%2Fhubble-image.webp&amp;w=828&amp;q=75 1x, /_next/image?url=%2Fhubble-image.webp&amp;w=1920&amp;q=75 2x" src="/_next/image?url=%2Fhubble-image.webp&amp;w=1920&amp;q=75"/></div></div></div><div class="flex flex-wrap justify-center gap-2 md:gap-3 mb-6 md:mb-8"><span class="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Javascript</span><span class="px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm">Express.js</span><span class="px-3 md:px-4 py-1.5 md:py-2 bg-teal-500/10 rounded-full text-teal-400 text-xs md:text-sm">Node.js</span><span class="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Mongo DB</span><span class="px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm">AWS</span><span class="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">TypeScript</span><span class="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">NATS</span><span class="px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm">Docker</span></div></div></div></div></div></section><section class="py-20 px-4"><div class="max-w-4xl mx-auto text-center"><div class="space-y-8" style="opacity:0;transform:translateY(20px)"><h2 class="text-3xl font-bold">Let&#x27;s Build Something Amazing</h2><p class="text-gray-400 max-w-2xl mx-auto">Looking for a software engineer who can architect and implement complete solutions? Let&#x27;s discuss your project.</p><div class="flex items-center justify-center gap-2 text-gray-400"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span>India</span></div><div class="flex flex-col sm:flex-row justify-center gap-4"><a href="mailto:<EMAIL>" class="px-8 py-3 bg-teal-500 rounded-lg font-medium hover:opacity-90 transition-opacity inline-flex items-center justify-center gap-2"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path><path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path></svg>Get in Touch</a><a href="/resume.pdf" target="_blank" class="px-8 py-3 bg-black border border-gray-800 rounded-lg font-medium hover:bg-gray-900 transition-colors inline-flex items-center justify-center gap-2"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M8 11a1 1 0 100 2h4a1 1 0 100-2H8zm0-4a1 1 0 100 2h4a1 1 0 100-2H8z" clip-rule="evenodd"></path></svg>View Resume</a></div><div class="flex justify-center gap-6"><a href="https://github.com/BenBasilTomy" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"></path></svg></a><a href="https://www.linkedin.com/in/ben-basil-tomy-5533b9218/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path></svg></a></div></div></div></section></main><script src="/_next/static/chunks/webpack-2fdd60e345297184.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[5244,[],\"\"]\n3:I[3866,[],\"\"]\n4:I[7033,[],\"ClientPageRoot\"]\n5:I[8930,[\"704\",\"static/chunks/704-c1b7454acf2285d3.js\",\"974\",\"static/chunks/app/page-a4cc83484493c8d6.js\"],\"default\"]\n8:I[6213,[],\"OutletBoundary\"]\na:I[6213,[],\"MetadataBoundary\"]\nc:I[6213,[],\"ViewportBoundary\"]\ne:I[4835,[],\"\"]\n:HL[\"/_next/static/css/499f96a3048ebeb3.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"EKL56wr9iWaZ9v0w6zHUC\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/499f96a3048ebeb3.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[],[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":\"$L9\"}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"Mz7QqA4qzhgnkS5JJ9OqP\",{\"children\":[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],null]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$e\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"6:{}\n7:{}\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"1\",{\"children\":\"[Ben Basil Tomy] - Software Engineer\"}],[\"$\",\"meta\",\"2\",{\"name\":\"description\",\"content\":\"Welcome to my portfolio! I am a passionate full-stack developer who bridges the gap between frontend and backend development. Specializing in creating complete web solutions, from beautiful user interfaces to robust server architectures.\"}],[\"$\",\"meta\",\"3\",{\"name\":\"author\",\"content\":\"[Ben Basil Tomy]\"}],[\"$\",\"meta\",\"4\",{\"name\":\"keywords\",\"content\":\"Full-Stack Developer,Software Engineer,Frontend Development,Backend Development,React,Next.js,Node.js,TypeScript,Database Design,API Development,Cloud Solutions,DevOps,System Architecture,Web Development,[Ben Basil Tomy]\"}],[\"$\",\"meta\",\"5\",{\"name\":\"creator\",\"content\":\"[Ben Basil Tomy]\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:title\",\"content\":\"[Ben Basil Tomy] - Full-Stack Developer Portfolio\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:description\",\"content\":\"Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack.\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:url\",\"content\":\"https://your-domain.com\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:site_name\",\"content\":\"[Ben Basil Tomy] - Portfolio\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:image\",\"content\":\"http://localhost:3000/og-image.jpg\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:alt\",\"content\":\"[Ben Basil Tomy] - Full-Stack Developer Portfolio\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:creator\",\"content\":\"@yourusername\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:title\",\"content\":\"[Ben Basil Tomy] - Full-Stack Developer\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:description\",\"content\":\"Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack.\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:image\",\"content\":\"http://localhost:3000/og-image.jpg\"}],[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]]\n"])</script><script>self.__next_f.push([1,"9:null\n"])</script></body></html>