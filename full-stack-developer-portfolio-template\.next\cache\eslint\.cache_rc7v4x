[{"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\ContactSection.tsx": "1", "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\ExperienceSection.tsx": "2", "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\HeroSection.tsx": "3", "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\ProjectsSection.tsx": "4", "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\SkillsSection.tsx": "5", "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\page.tsx": "7"}, {"size": 5162, "mtime": 1754076462732, "results": "8", "hashOfConfig": "9"}, {"size": 10847, "mtime": 1754076626835, "results": "10", "hashOfConfig": "9"}, {"size": 10642, "mtime": 1754068548088, "results": "11", "hashOfConfig": "9"}, {"size": 11374, "mtime": 1754028598997, "results": "12", "hashOfConfig": "9"}, {"size": 4214, "mtime": 1754287207831, "results": "13", "hashOfConfig": "9"}, {"size": 2484, "mtime": 1754045523457, "results": "14", "hashOfConfig": "9"}, {"size": 561, "mtime": 1754078086118, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k117pi", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\ContactSection.tsx", [], [], "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\ExperienceSection.tsx", ["37"], [], "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\ProjectsSection.tsx", [], [], "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\components\\SkillsSection.tsx", [], [], "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\page.tsx", [], [], {"ruleId": "38", "severity": 2, "message": "39", "line": 8, "column": 10, "nodeType": null, "messageId": "40", "endLine": 8, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'selectedExp' is assigned a value but never used.", "unusedVar"]