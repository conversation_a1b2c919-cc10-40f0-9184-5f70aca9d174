(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},9551:e=>{"use strict";e.exports=require("url")},5326:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>h,routeModule:()=>d,tree:()=>u});var n=i(260),r=i(8203),s=i(5155),a=i.n(s),o=i(7292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,5104)),"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,9611)),"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,1485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],h=["C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6972:(e,t,i)=>{Promise.resolve().then(i.bind(i,5104))},3820:(e,t,i)=>{Promise.resolve().then(i.bind(i,9096))},8375:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,3219,23)),Promise.resolve().then(i.t.bind(i,4863,23)),Promise.resolve().then(i.t.bind(i,5155,23)),Promise.resolve().then(i.t.bind(i,802,23)),Promise.resolve().then(i.t.bind(i,9350,23)),Promise.resolve().then(i.t.bind(i,8530,23)),Promise.resolve().then(i.t.bind(i,8921,23))},3103:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6959,23)),Promise.resolve().then(i.t.bind(i,3875,23)),Promise.resolve().then(i.t.bind(i,8903,23)),Promise.resolve().then(i.t.bind(i,7174,23)),Promise.resolve().then(i.t.bind(i,4178,23)),Promise.resolve().then(i.t.bind(i,7190,23)),Promise.resolve().then(i.t.bind(i,1365,23))},9375:()=>{},6327:()=>{},9096:(e,t,i)=>{"use strict";let n,r,s;i.r(t),i.d(t,{default:()=>as});var a=i(5512),o=i(8009);let l=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],u=new Set(l),h=e=>180*e/Math.PI,c=e=>p(h(Math.atan2(e[1],e[0]))),d={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:c,rotateZ:c,skewX:e=>h(Math.atan(e[1])),skewY:e=>h(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},p=e=>((e%=360)<0&&(e+=360),e),f=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),m=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),g={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:f,scaleY:m,scale:e=>(f(e)+m(e))/2,rotateX:e=>p(h(Math.atan2(e[6],e[5]))),rotateY:e=>p(h(Math.atan2(-e[2],e[0]))),rotateZ:c,rotate:c,skewX:e=>h(Math.atan(e[4])),skewY:e=>h(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function y(e){return e.includes("scale")?1:0}function v(e,t){let i,n;if(!e||"none"===e)return y(t);let r=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=g,n=r;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=d,n=t}if(!n)return y(t);let s=i[t],a=n[1].split(",").map(b);return"function"==typeof s?s(a):a[s]}let x=(e,t)=>{let{transform:i="none"}=getComputedStyle(e);return v(i,t)};function b(e){return parseFloat(e.trim())}let w=e=>t=>"string"==typeof t&&t.startsWith(e),P=w("--"),S=w("var(--"),E=e=>!!S(e)&&T.test(e.split("/*")[0].trim()),T=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function j({top:e,left:t,right:i,bottom:n}){return{x:{min:t,max:i},y:{min:e,max:n}}}let A=(e,t,i)=>e+(t-e)*i;function M(e){return void 0===e||1===e}function R({scale:e,scaleX:t,scaleY:i}){return!M(e)||!M(t)||!M(i)}function C(e){return R(e)||k(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function k(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function N(e,t,i,n,r){return void 0!==r&&(e=n+r*(e-n)),n+i*(e-n)+t}function D(e,t=0,i=1,n,r){e.min=N(e.min,t,i,n,r),e.max=N(e.max,t,i,n,r)}function O(e,{x:t,y:i}){D(e.x,t.translate,t.scale,t.originPoint),D(e.y,i.translate,i.scale,i.originPoint)}function _(e,t){e.min=e.min+t,e.max=e.max+t}function V(e,t,i,n,r=.5){let s=A(e.min,e.max,r);D(e,t,i,s,n)}function L(e,t){V(e.x,t.x,t.scaleX,t.scale,t.originX),V(e.y,t.y,t.scaleY,t.scale,t.originY)}function I(e,t){return j(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let F=new Set(["width","height","top","left","right","bottom",...l]),B=(e,t,i)=>i>t?t:i<e?e:i,$={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},U={...$,transform:e=>B(0,1,e)},z={...$,default:1},W=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),H=W("deg"),X=W("%"),q=W("px"),Y=W("vh"),G=W("vw"),K={...X,parse:e=>X.parse(e)/100,transform:e=>X.transform(100*e)},Z=e=>t=>t.test(e),Q=[$,q,X,H,G,Y,{test:e=>"auto"===e,parse:e=>e}],J=e=>Q.find(Z(e)),ee=()=>{},et=()=>{},ei=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),en=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,er=e=>e===$||e===q,es=new Set(["x","y","z"]),ea=l.filter(e=>!es.has(e)),eo={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>v(t,"x"),y:(e,{transform:t})=>v(t,"y")};eo.translateX=eo.x,eo.translateY=eo.y;let el=e=>e,eu={},eh=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ec={value:null,addProjectionMetrics:null};function ed(e,t){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=eh.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,n=new Set,r=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(h.schedule(t),e()),l++,t(o)}let h={schedule:(e,t=!1,s=!1)=>{let o=s&&r?i:n;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{n.delete(e),a.delete(e)},process:e=>{if(o=e,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),t&&ec.value&&ec.value.frameloop[t].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(e))}};return h}(s,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:h,update:c,preRender:d,render:p,postRender:f}=a,m=()=>{let s=eu.useManualTiming?r.timestamp:performance.now();i=!1,eu.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,o.process(r),l.process(r),u.process(r),h.process(r),c.process(r),d.process(r),p.process(r),f.process(r),r.isProcessing=!1,i&&t&&(n=!1,e(m))},g=()=>{i=!0,n=!0,r.isProcessing||e(m)};return{schedule:eh.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,r=!1)=>(i||g(),n.schedule(e,t,r)),e},{}),cancel:e=>{for(let t=0;t<eh.length;t++)a[eh[t]].cancel(e)},state:r,steps:a}}let{schedule:ep,cancel:ef,state:em,steps:eg}=ed("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:el,!0),ey=new Set,ev=!1,ex=!1,eb=!1;function ew(){if(ex){let e=Array.from(ey).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return ea.forEach(i=>{let n=e.getValue(i);void 0!==n&&(t.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}ex=!1,ev=!1,ey.forEach(e=>e.complete(eb)),ey.clear()}function eP(){ey.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(ex=!0)})}class eS{constructor(e,t,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(ey.add(this),ev||(ev=!0,ep.read(eP),ep.resolveKeyframes(ew))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:n}=this;if(null===e[0]){let r=n?.get(),s=e[e.length-1];if(void 0!==r)e[0]=r;else if(i&&t){let n=i.readValue(t,s);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=s),n&&void 0===r&&n.set(e[0])}!function(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ey.delete(this)}cancel(){"scheduled"===this.state&&(ey.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eE=e=>/^0[^.\s]+$/u.test(e),eT=e=>Math.round(1e5*e)/1e5,ej=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eA=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eM=(e,t)=>i=>!!("string"==typeof i&&eA.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),eR=(e,t,i)=>n=>{if("string"!=typeof n)return n;let[r,s,a,o]=n.match(ej);return{[e]:parseFloat(r),[t]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},eC=e=>B(0,255,e),ek={...$,transform:e=>Math.round(eC(e))},eN={test:eM("rgb","red"),parse:eR("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:n=1})=>"rgba("+ek.transform(e)+", "+ek.transform(t)+", "+ek.transform(i)+", "+eT(U.transform(n))+")"},eD={test:eM("#"),parse:function(e){let t="",i="",n="",r="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),n=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),n=e.substring(3,4),r=e.substring(4,5),t+=t,i+=i,n+=n,r+=r),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:eN.transform},eO={test:eM("hsl","hue"),parse:eR("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:n=1})=>"hsla("+Math.round(e)+", "+X.transform(eT(t))+", "+X.transform(eT(i))+", "+eT(U.transform(n))+")"},e_={test:e=>eN.test(e)||eD.test(e)||eO.test(e),parse:e=>eN.test(e)?eN.parse(e):eO.test(e)?eO.parse(e):eD.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eN.transform(e):eO.transform(e),getAnimatableNone:e=>{let t=e_.parse(e);return t.alpha=0,e_.transform(t)}},eV=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eL="number",eI="color",eF=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eB(e){let t=e.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,a=t.replace(eF,e=>(e_.test(e)?(n.color.push(s),r.push(eI),i.push(e_.parse(e))):e.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(e)):(n.number.push(s),r.push(eL),i.push(parseFloat(e))),++s,"${}")).split("${}");return{values:i,split:a,indexes:n,types:r}}function e$(e){return eB(e).values}function eU(e){let{split:t,types:i}=eB(e),n=t.length;return e=>{let r="";for(let s=0;s<n;s++)if(r+=t[s],void 0!==e[s]){let t=i[s];t===eL?r+=eT(e[s]):t===eI?r+=e_.transform(e[s]):r+=e[s]}return r}}let ez=e=>"number"==typeof e?0:e_.test(e)?e_.getAnimatableNone(e):e,eW={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(ej)?.length||0)+(e.match(eV)?.length||0)>0},parse:e$,createTransformer:eU,getAnimatableNone:function(e){let t=e$(e);return eU(e)(t.map(ez))}},eH=new Set(["brightness","contrast","saturate","opacity"]);function eX(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=i.match(ej)||[];if(!n)return e;let r=i.replace(n,""),s=eH.has(t)?1:0;return n!==i&&(s*=100),t+"("+s+r+")"}let eq=/\b([a-z-]*)\(.*?\)/gu,eY={...eW,getAnimatableNone:e=>{let t=e.match(eq);return t?t.map(eX).join(" "):e}},eG={...$,transform:Math.round},eK={borderWidth:q,borderTopWidth:q,borderRightWidth:q,borderBottomWidth:q,borderLeftWidth:q,borderRadius:q,radius:q,borderTopLeftRadius:q,borderTopRightRadius:q,borderBottomRightRadius:q,borderBottomLeftRadius:q,width:q,maxWidth:q,height:q,maxHeight:q,top:q,right:q,bottom:q,left:q,padding:q,paddingTop:q,paddingRight:q,paddingBottom:q,paddingLeft:q,margin:q,marginTop:q,marginRight:q,marginBottom:q,marginLeft:q,backgroundPositionX:q,backgroundPositionY:q,rotate:H,rotateX:H,rotateY:H,rotateZ:H,scale:z,scaleX:z,scaleY:z,scaleZ:z,skew:H,skewX:H,skewY:H,distance:q,translateX:q,translateY:q,translateZ:q,x:q,y:q,z:q,perspective:q,transformPerspective:q,opacity:U,originX:K,originY:K,originZ:q,zIndex:eG,fillOpacity:U,strokeOpacity:U,numOctaves:eG},eZ={...eK,color:e_,backgroundColor:e_,outlineColor:e_,fill:e_,stroke:e_,borderColor:e_,borderTopColor:e_,borderRightColor:e_,borderBottomColor:e_,borderLeftColor:e_,filter:eY,WebkitFilter:eY},eQ=e=>eZ[e];function eJ(e,t){let i=eQ(e);return i!==eY&&(i=eW),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let e0=new Set(["auto","none","0"]);class e1 extends eS{constructor(e,t,i,n,r){super(e,t,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let n=e[i];if("string"==typeof n&&E(n=n.trim())){let r=function e(t,i,n=1){et(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[r,s]=function(e){let t=en.exec(e);if(!t)return[,];let[,i,n,r]=t;return[`--${i??n}`,r]}(t);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let e=a.trim();return ei(e)?parseFloat(e):e}return E(s)?e(s,i,n+1):s}(n,t.current);void 0!==r&&(e[i]=r),i===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!F.has(i)||2!==e.length)return;let[n,r]=e,s=J(n),a=J(r);if(s!==a){if(er(s)&&er(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else eo[i]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var n;(null===e[t]||("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||eE(n)))&&i.push(t)}i.length&&function(e,t,i){let n,r=0;for(;r<e.length&&!n;){let t=e[r];"string"==typeof t&&!e0.has(t)&&eB(t).values.length&&(n=e[r]),r++}if(n&&i)for(let r of t)e[r]=eJ(i,n)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eo[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(i,n).jump(n,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eo[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let e2=e=>!!(e&&e.getVelocity);function e5(){n=void 0}let e3={now:()=>(void 0===n&&e3.set(em.isProcessing||eu.useManualTiming?em.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(e5)}};function e4(e,t){-1===e.indexOf(t)&&e.push(t)}function e6(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class e8{constructor(){this.subscriptions=[]}add(e){return e4(this.subscriptions,e),()=>e6(this.subscriptions,e)}notify(e,t,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(e,t,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function e9(e,t){return t?1e3/t*e:0}let e7=e=>!isNaN(parseFloat(e)),te={current:void 0};class tt{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=e=>{let t=e3.now();if(this.updatedAt!==t&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=e3.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=e7(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new e8);let i=this.events[e].add(t);return"change"===e?()=>{i(),ep.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return te.current&&te.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let e=e3.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e9(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ti(e,t){return new tt(e,t)}let tn=[...Q,e_,eW],tr=e=>tn.find(Z(e)),{schedule:ts}=ed(queueMicrotask,!1),ta={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},to={};for(let e in ta)to[e]={isEnabled:t=>ta[e].some(e=>!!t[e])};let tl=()=>({translate:0,scale:1,origin:0,originPoint:0}),tu=()=>({x:tl(),y:tl()}),th=()=>({min:0,max:0}),tc=()=>({x:th(),y:th()}),td="undefined"!=typeof window,tp={current:null},tf={current:!1},tm=new WeakMap;function tg(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function ty(e){return"string"==typeof e||Array.isArray(e)}let tv=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tx=["initial",...tv];function tb(e){return tg(e.animate)||tx.some(t=>ty(e[t]))}function tw(e){return!!(tb(e)||e.variants)}function tP(e,t,i,n){if("function"==typeof t||("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t)){let[r,s]=function(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}(n);t=t(void 0!==i?i:e.custom,r,s)}return t}let tS=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tE{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eS,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=e3.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,ep.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=tb(t),this.isVariantNode=tw(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in h){let t=h[e];void 0!==o[e]&&e2(t)&&t.set(o[e])}}mount(e){this.current=e,tm.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),tf.current||function(){if(tf.current=!0,td){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>tp.current=e.matches;e.addEventListener("change",t),t()}else tp.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||tp.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),ef(this.notifyUpdate),ef(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=u.has(e);n&&this.onBindTransform&&this.onBindTransform();let r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&ep.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in to){let t=to[e];if(!t)continue;let{isEnabled:i,Feature:n}=t;if(!this.features[e]&&n&&i(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tc()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<tS.length;t++){let i=tS[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=e["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(e,t,i){for(let n in t){let r=t[n],s=i[n];if(e2(r))e.addValue(n,r);else if(e2(s))e.addValue(n,ti(r,{owner:e}));else if(s!==r){if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(n);e.addValue(n,ti(void 0!==t?t:r,{owner:e}))}}}for(let n in i)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=ti(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=i&&("string"==typeof i&&(ei(i)||eE(i))?i=parseFloat(i):!tr(i)&&eW.test(t)&&(i=eJ(e,t)),this.setBaseTarget(e,e2(i)?i.get():i)),e2(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=tP(this.props,i,this.presenceContext?.custom);n&&(t=n[e])}if(i&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||e2(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new e8),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}scheduleRenderMicrotask(){ts.render(this.render)}}class tT extends tE{constructor(){super(...arguments),this.KeyframeResolver=e1}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;e2(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}let tj=(e,t)=>t&&"number"==typeof e?t.transform(e):e,tA={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tM=l.length;function tR(e,t,i){let{style:n,vars:r,transformOrigin:s}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(u.has(e)){a=!0;continue}if(P(e)){r[e]=i;continue}{let t=tj(i,eK[e]);e.startsWith("origin")?(o=!0,s[e]=t):n[e]=t}}if(!t.transform&&(a||i?n.transform=function(e,t,i){let n="",r=!0;for(let s=0;s<tM;s++){let a=l[s],o=e[a];if(void 0===o)continue;let u=!0;if(!(u="number"==typeof o?o===(a.startsWith("scale")?1:0):0===parseFloat(o))||i){let e=tj(o,eK[a]);if(!u){r=!1;let t=tA[a]||a;n+=`${t}(${e}) `}i&&(t[a]=e)}}return n=n.trim(),i?n=i(t,r?"":n):r&&(n="none"),n}(t,e.transform,i):n.transform&&(n.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=s;n.transformOrigin=`${e} ${t} ${i}`}}function tC(e,{style:t,vars:i},n,r){let s;let a=e.style;for(s in t)a[s]=t[s];for(s in r?.applyProjectionStyles(a,n),i)a.setProperty(s,i[s])}let tk={};function tN(e,{layout:t,layoutId:i}){return u.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!tk[e]||"opacity"===e)}function tD(e,t,i){let{style:n}=e,r={};for(let s in n)(e2(n[s])||t.style&&e2(t.style[s])||tN(s,e)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}class tO extends tT{constructor(){super(...arguments),this.type="html",this.renderInstance=tC}readValueFromInstance(e,t){if(u.has(t))return this.projection?.isProjecting?y(t):x(e,t);{let i=window.getComputedStyle(e),n=(P(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return I(e,t)}build(e,t,i){tR(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return tD(e,t,i)}}let t_=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tV={offset:"stroke-dashoffset",array:"stroke-dasharray"},tL={offset:"strokeDashoffset",array:"strokeDasharray"};function tI(e,{attrX:t,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:a=0,...o},l,u,h){if(tR(e,o,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:c,style:d}=e;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==t&&(c.x=t),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(e,t,i=1,n=0,r=!0){e.pathLength=1;let s=r?tV:tL;e[s.offset]=q.transform(-n);let a=q.transform(t),o=q.transform(i);e[s.array]=`${a} ${o}`}(c,r,s,a,!1)}let tF=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),tB=e=>"string"==typeof e&&"svg"===e.toLowerCase();function t$(e,t,i){let n=tD(e,t,i);for(let i in e)(e2(e[i])||e2(t[i]))&&(n[-1!==l.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return n}class tU extends tT{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tc}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(u.has(t)){let e=eQ(t);return e&&e.default||0}return t=tF.has(t)?t:t_(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return t$(e,t,i)}build(e,t,i){tI(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,n){!function(e,t,i,n){for(let i in tC(e,t,void 0,n),t.attrs)e.setAttribute(tF.has(i)?i:t_(i),t.attrs[i])}(e,t,0,n)}mount(e){this.isSVGTag=tB(e.tagName),super.mount(e)}}let tz=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tW(e){if("string"!=typeof e||e.includes("-"));else if(tz.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let tH=(0,o.createContext)({}),tX=(0,o.createContext)({strict:!1}),tq=(0,o.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),tY=(0,o.createContext)({});function tG(e){return Array.isArray(e)?e.join(" "):e}let tK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tZ(e,t,i){for(let n in t)e2(t[n])||tN(n,i)||(e[n]=t[n])}let tQ=()=>({...tK(),attrs:{}}),tJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function t0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||tJ.has(e)}let t1=e=>!t0(e);try{!function(e){"function"==typeof e&&(t1=t=>t.startsWith("on")?!t0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let t2=(0,o.createContext)(null);function t5(e){let t=(0,o.useRef)(null);return null===t.current&&(t.current=e()),t.current}function t3(e){return e2(e)?e.get():e}let t4=e=>(t,i)=>{let n=(0,o.useContext)(tY),r=(0,o.useContext)(t2),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,n,r){return{latestValues:function(e,t,i,n){let r={},s=n(e,{});for(let e in s)r[e]=t3(s[e]);let{initial:a,animate:o}=e,l=tb(e),u=tw(e);t&&u&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===o&&(o=t.animate));let h=!!i&&!1===i.initial,c=(h=h||!1===a)?o:a;if(c&&"boolean"!=typeof c&&!tg(c)){let t=Array.isArray(c)?c:[c];for(let i=0;i<t.length;i++){let n=tP(e,t[i]);if(n){let{transitionEnd:e,transition:t,...i}=n;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(r[e]=t)}for(let t in e)r[t]=e[t]}}}return r}(i,n,r,e),renderState:t()}})(e,t,n,r);return i?s():t5(s)},t6=t4({scrapeMotionValuesFromProps:tD,createRenderState:tK}),t8=t4({scrapeMotionValuesFromProps:t$,createRenderState:tQ}),t9=Symbol.for("motionComponentSymbol");function t7(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let ie="data-"+t_("framerAppearId"),it=(0,o.createContext)({}),ii=td?o.useLayoutEffect:o.useEffect;function ir(e,{forwardMotionProps:t=!1}={},i,n){i&&function(e){for(let t in e)to[t]={...to[t],...e[t]}}(i);let r=tW(e)?t8:t6;function s(i,s){var l;let u;let h={...(0,o.useContext)(tq),...i,layoutId:function({layoutId:e}){let t=(0,o.useContext)(tH).id;return t&&void 0!==e?t+"-"+e:e}(i)},{isStatic:c}=h,d=function(e){let{initial:t,animate:i}=function(e,t){if(tb(e)){let{initial:t,animate:i}=e;return{initial:!1===t||ty(t)?t:void 0,animate:ty(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,o.useContext)(tY));return(0,o.useMemo)(()=>({initial:t,animate:i}),[tG(t),tG(i)])}(i),p=r(i,c);if(!c&&td){(0,o.useContext)(tX).strict;let t=function(e){let{drag:t,layout:i}=to;if(!t&&!i)return{};let n={...t,...i};return{MeasureLayout:t?.isEnabled(e)||i?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,d.visualElement=function(e,t,i,n,r){let{visualElement:s}=(0,o.useContext)(tY),a=(0,o.useContext)(tX),l=(0,o.useContext)(t2),u=(0,o.useContext)(tq).reducedMotion,h=(0,o.useRef)(null);n=n||a.renderer,!h.current&&n&&(h.current=n(e,{visualState:t,parent:s,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let c=h.current,d=(0,o.useContext)(it);c&&!c.projection&&r&&("html"===c.type||"svg"===c.type)&&function(e,t,i,n){let{layoutId:r,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!a||o&&t7(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,r,d);let p=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{c&&p.current&&c.update(i,l)});let f=i[ie],m=(0,o.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return ii(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),c.scheduleRenderMicrotask(),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,o.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),m.current=!1),c.enteringChildren=void 0)}),c}(e,p,h,n,t.ProjectionNode)}return(0,a.jsxs)(tY.Provider,{value:d,children:[u&&d.visualElement?(0,a.jsx)(u,{visualElement:d.visualElement,...h}):null,function(e,t,i,{latestValues:n},r,s=!1){let a=(tW(e)?function(e,t,i,n){let r=(0,o.useMemo)(()=>{let i=tQ();return tI(i,t,tB(n),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};tZ(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let i={},n=function(e,t){let i=e.style||{},n={};return tZ(n,i,e),Object.assign(n,function({transformTemplate:e},t){return(0,o.useMemo)(()=>{let i=tK();return tR(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=n,i})(t,n,r,e),l=function(e,t,i){let n={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(t1(r)||!0===i&&t0(r)||!t&&!t0(r)||e.draggable&&r.startsWith("onDrag"))&&(n[r]=e[r]);return n}(t,"string"==typeof e,s),u=e!==o.Fragment?{...l,...a,ref:i}:{},{children:h}=t,c=(0,o.useMemo)(()=>e2(h)?h.get():h,[h]);return(0,o.createElement)(e,{...u,children:c})}(e,i,(l=d.visualElement,(0,o.useCallback)(e=>{e&&p.onMount&&p.onMount(e),l&&(e?l.mount(e):l.unmount()),s&&("function"==typeof s?s(e):t7(s)&&(s.current=e))},[l])),p,c,t)]})}s.displayName=`motion.${"string"==typeof e?e:`create(${e.displayName??e.name??""})`}`;let l=(0,o.forwardRef)(s);return l[t9]=e,l}function is(e,t,i){let n=e.getProps();return tP(n,t,void 0!==i?i:n.custom,e)}function ia(e,t){return e?.[t]??e?.default??e}let io=e=>Array.isArray(e);function il(e,t){let i=e.getValue("willChange");if(e2(i)&&i.add)return i.add(t);if(!i&&eu.WillChange){let i=new eu.WillChange("auto");e.addValue("willChange",i),i.add(t)}}function iu(e){e.duration=0,e.type}let ih=(e,t)=>i=>t(e(i)),ic=(...e)=>e.reduce(ih),id=e=>1e3*e,ip=e=>e/1e3,im={layout:0,mainThread:0,waapi:0};function ig(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function iy(e,t){return i=>i>0?t:e}let iv=(e,t,i)=>{let n=e*e,r=i*(t*t-n)+n;return r<0?0:Math.sqrt(r)},ix=[eD,eN,eO],ib=e=>ix.find(t=>t.test(e));function iw(e){let t=ib(e);if(ee(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!t)return!1;let i=t.parse(e);return t===eO&&(i=function({hue:e,saturation:t,lightness:i,alpha:n}){e/=360,i/=100;let r=0,s=0,a=0;if(t/=100){let n=i<.5?i*(1+t):i+t-i*t,o=2*i-n;r=ig(o,n,e+1/3),s=ig(o,n,e),a=ig(o,n,e-1/3)}else r=s=a=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*a),alpha:n}}(i)),i}let iP=(e,t)=>{let i=iw(e),n=iw(t);if(!i||!n)return iy(e,t);let r={...i};return e=>(r.red=iv(i.red,n.red,e),r.green=iv(i.green,n.green,e),r.blue=iv(i.blue,n.blue,e),r.alpha=A(i.alpha,n.alpha,e),eN.transform(r))},iS=new Set(["none","hidden"]);function iE(e,t){return i=>A(e,t,i)}function iT(e){return"number"==typeof e?iE:"string"==typeof e?E(e)?iy:e_.test(e)?iP:iM:Array.isArray(e)?ij:"object"==typeof e?e_.test(e)?iP:iA:iy}function ij(e,t){let i=[...e],n=i.length,r=e.map((e,i)=>iT(e)(e,t[i]));return e=>{for(let t=0;t<n;t++)i[t]=r[t](e);return i}}function iA(e,t){let i={...e,...t},n={};for(let r in i)void 0!==e[r]&&void 0!==t[r]&&(n[r]=iT(e[r])(e[r],t[r]));return e=>{for(let t in n)i[t]=n[t](e);return i}}let iM=(e,t)=>{let i=eW.createTransformer(t),n=eB(e),r=eB(t);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?iS.has(e)&&!r.values.length||iS.has(t)&&!n.values.length?function(e,t){return iS.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):ic(ij(function(e,t){let i=[],n={color:0,var:0,number:0};for(let r=0;r<t.values.length;r++){let s=t.types[r],a=e.indexes[s][n[s]],o=e.values[a]??0;i[r]=o,n[s]++}return i}(n,r),r.values),i):(ee(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),iy(e,t))};function iR(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?A(e,t,i):iT(e)(e,t)}let iC=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>ep.update(t,e),stop:()=>ef(t),now:()=>em.isProcessing?em.timestamp:e3.now()}},ik=(e,t,i=10)=>{let n="",r=Math.max(Math.round(t/i),2);for(let t=0;t<r;t++)n+=Math.round(1e4*e(t/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function iN(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function iD(e,t,i){let n=Math.max(t-5,0);return e9(i-e(n),t-n)}let iO={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function i_(e,t){return e*Math.sqrt(1-t*t)}let iV=["duration","bounce"],iL=["stiffness","damping","mass"];function iI(e,t){return t.some(t=>void 0!==e[t])}function iF(e=iO.visualDuration,t=iO.bounce){let i;let n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:r,restDelta:s}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:f}=function(e){let t={velocity:iO.velocity,stiffness:iO.stiffness,damping:iO.damping,mass:iO.mass,isResolvedFromDuration:!1,...e};if(!iI(e,iL)&&iI(e,iV)){if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),n=i*i,r=2*B(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:iO.mass,stiffness:n,damping:r}}else{let i=function({duration:e=iO.duration,bounce:t=iO.bounce,velocity:i=iO.velocity,mass:n=iO.mass}){let r,s;ee(e<=id(iO.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let a=1-t;a=B(iO.minDamping,iO.maxDamping,a),e=B(iO.minDuration,iO.maxDuration,ip(e)),a<1?(r=t=>{let n=t*a,r=n*e;return .001-(n-i)/i_(t,a)*Math.exp(-r)},s=t=>{let n=t*a*e,s=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-n),l=i_(Math.pow(t,2),a);return(n*i+i-s)*o*(-r(t)+.001>0?-1:1)/l}):(r=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),s=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let n=i;for(let i=1;i<12;i++)n-=e(n)/t(n);return n}(r,s,5/e);if(e=id(e),isNaN(o))return{stiffness:iO.stiffness,damping:iO.damping,duration:e};{let t=Math.pow(o,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...i,mass:iO.mass}).isResolvedFromDuration=!0}}return t}({...n,velocity:-ip(n.velocity||0)}),m=p||0,g=h/(2*Math.sqrt(u*c)),y=o-a,v=ip(Math.sqrt(u/c)),x=5>Math.abs(y);if(r||(r=x?iO.restSpeed.granular:iO.restSpeed.default),s||(s=x?iO.restDelta.granular:iO.restDelta.default),g<1){let e=i_(v,g);i=t=>o-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)i=e=>o-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);i=t=>{let i=Math.exp(-g*v*t),n=Math.min(e*t,300);return o-i*((m+g*v*y)*Math.sinh(n)+e*y*Math.cosh(n))/e}}let b={calculatedDuration:f&&d||null,next:e=>{let t=i(e);if(f)l.done=e>=d;else{let n=0===e?m:0;g<1&&(n=0===e?id(m):iD(i,e,t));let a=Math.abs(n)<=r,u=Math.abs(o-t)<=s;l.done=a&&u}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(iN(b),2e4),t=ik(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function iB({keyframes:e,velocity:t=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=e[0],f={done:!1,value:p},m=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l?o:Math.abs(o-e)<Math.abs(l-e)?o:l,y=i*t,v=p+y,x=void 0===a?v:a(v);x!==v&&(y=x-p);let b=e=>-y*Math.exp(-e/n),w=e=>x+b(e),P=e=>{let t=b(e),i=w(e);f.done=Math.abs(t)<=u,f.value=f.done?x:i},S=e=>{m(f.value)&&(c=e,d=iF({keyframes:[f.value,g(f.value)],velocity:iD(w,e,f.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return S(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==c||(t=!0,P(e),S(e)),void 0!==c&&e>=c)?d.next(e-c):(t||P(e),f)}}}iF.applyToOptions=e=>{let t=function(e,t=100,i){let n=i({...e,keyframes:[0,t]}),r=Math.min(iN(n),2e4);return{type:"keyframes",ease:e=>n.next(r*e).value/t,duration:ip(r)}}(e,100,iF);return e.ease=t.ease,e.duration=id(t.duration),e.type="keyframes",e};let i$=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function iU(e,t,i,n){if(e===t&&i===n)return el;let r=t=>(function(e,t,i,n,r){let s,a;let o=0;do(s=i$(a=t+(i-t)/2,n,r)-e)>0?i=a:t=a;while(Math.abs(s)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:i$(r(e),t,n)}let iz=iU(.42,0,1,1),iW=iU(0,0,.58,1),iH=iU(.42,0,.58,1),iX=e=>Array.isArray(e)&&"number"!=typeof e[0],iq=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,iY=e=>t=>1-e(1-t),iG=iU(.33,1.53,.69,.99),iK=iY(iG),iZ=iq(iK),iQ=e=>(e*=2)<1?.5*iK(e):.5*(2-Math.pow(2,-10*(e-1))),iJ=e=>1-Math.sin(Math.acos(e)),i0=iY(iJ),i1=iq(iJ),i2=e=>Array.isArray(e)&&"number"==typeof e[0],i5={linear:el,easeIn:iz,easeInOut:iH,easeOut:iW,circIn:iJ,circInOut:i1,circOut:i0,backIn:iK,backInOut:iZ,backOut:iG,anticipate:iQ},i3=e=>"string"==typeof e,i4=e=>{if(i2(e)){et(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,i,n,r]=e;return iU(t,i,n,r)}return i3(e)?(et(void 0!==i5[e],`Invalid easing type '${e}'`,"invalid-easing-type"),i5[e]):e},i6=(e,t,i)=>{let n=t-e;return 0===n?1:(i-e)/n};function i8(e,t,{clamp:i=!0,ease:n,mixer:r}={}){let s=e.length;if(et(s===t.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let n=[],r=i||eu.mix||iR,s=e.length-1;for(let i=0;i<s;i++){let s=r(e[i],e[i+1]);t&&(s=ic(Array.isArray(t)?t[i]||el:t,s)),n.push(s)}return n}(t,n,r),l=o.length,u=i=>{if(a&&i<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(i<e[n+1]);n++);let r=i6(e[n],e[n+1],i);return o[n](r)};return i?t=>u(B(e[0],e[s-1],t)):u}function i9(e){let t=[0];return function(e,t){let i=e[e.length-1];for(let n=1;n<=t;n++){let r=i6(0,t,n);e.push(A(i,1,r))}}(t,e.length-1),t}function i7({duration:e=300,keyframes:t,times:i,ease:n="easeInOut"}){let r=iX(n)?n.map(i4):i4(n),s={done:!1,value:t[0]},a=i8((i&&i.length===t.length?i:i9(t)).map(t=>t*e),t,{ease:Array.isArray(r)?r:t.map(()=>r||iH).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=a(t),s.done=t>=e,s)}}let ne=e=>null!==e;function nt(e,{repeat:t,repeatType:i="loop"},n,r=1){let s=e.filter(ne),a=r<0||t&&"loop"!==i&&t%2==1?0:s.length-1;return a&&void 0!==n?n:s[a]}let ni={decay:iB,inertia:iB,tween:i7,keyframes:i7,spring:iF};function nn(e){"string"==typeof e.type&&(e.type=ni[e.type])}class nr{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let ns=e=>e/100;class na extends nr{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==e3.now()&&this.tick(e3.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},im.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;nn(e);let{type:t=i7,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=e,{keyframes:a}=e,o=t||i7;o!==i7&&"number"!=typeof a[0]&&(this.mixKeyframes=ic(ns,iR(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===r&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=iN(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let e=Math.min(this.currentTime,n)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,h+1))%2&&("reverse"===c?(i=1-i,d&&(i-=d/a)):"mirror"===c&&(x=s)),v=B(0,1,i)*a}let b=y?{done:!1,value:u[0]}:x.next(v);r&&(b.value=r(b.value));let{done:w}=b;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&p!==iB&&(b.value=nt(u,this.options,m,this.speed)),f&&f(b.value),P&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return ip(this.calculatedDuration)}get time(){return ip(this.currentTime)}set time(e){e=id(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(e3.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=ip(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=iC,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(e3.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,im.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let no=e=>e.startsWith("--");function nl(e){let t;return()=>(void 0===t&&(t=e()),t)}let nu=nl(()=>void 0!==window.ScrollTimeline),nh={},nc=function(e,t){let i=nl(e);return()=>nh[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),nd=([e,t,i,n])=>`cubic-bezier(${e}, ${t}, ${i}, ${n})`,np={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:nd([0,.65,.55,1]),circOut:nd([.55,0,1,.45]),backIn:nd([.31,.01,.66,-.59]),backOut:nd([.33,1.53,.69,.99])};function nf(e){return"function"==typeof e&&"applyToOptions"in e}class nm extends nr{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=e,et("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:e,...t}){return nf(e)&&nc()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let h={[t]:i};l&&(h.offset=l);let c=function e(t,i){if(t)return"function"==typeof t?nc()?ik(t,i):"ease-out":i2(t)?nd(t):Array.isArray(t)?t.map(t=>e(t,i)||np.easeOut):np[t]}(o,r);Array.isArray(c)&&(h.easing=c),ec.value&&im.waapi++;let d={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};u&&(d.pseudoElement=u);let p=e.animate(h,d);return ec.value&&p.finished.finally(()=>{im.waapi--}),p}(t,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let e=nt(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){no(t)?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return ip(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return ip(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=id(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&nu())?(this.animation.timeline=e,el):t(this)}}let ng={anticipate:iQ,backInOut:iZ,circInOut:i1};class ny extends nm{constructor(e){(function(e){"string"==typeof e.ease&&e.ease in ng&&(e.ease=ng[e.ease])})(e),nn(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!t)return;if(void 0!==e){t.set(e);return}let a=new na({...s,autoplay:!1}),o=id(this.finishedTime??this.time);t.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let nv=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eW.test(e)||"0"===e)&&!e.startsWith("url(")),nx=new Set(["opacity","clipPath","filter","transform"]),nb=nl(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class nw extends nr{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=e3.now();let c={autoplay:e,delay:t,type:i,repeat:n,repeatDelay:r,repeatType:s,name:o,motionValue:l,element:u,...h},d=u?.KeyframeResolver||eS;this.keyframeResolver=new d(a,(e,t,i)=>this.onKeyframesResolved(e,t,c,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=e3.now(),!function(e,t,i,n){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],a=nv(r,t),o=nv(s,t);return ee(a===o,`You are trying to animate ${t} from "${r}" to "${s}". "${a?s:r}" is not an animatable value.`,"value-not-animatable"),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||nf(i))&&n)}(e,r,s,a)&&((eu.instantAnimations||!o)&&u?.(nt(e,i,t)),e[0]=e[e.length-1],iu(i),i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},c=!l&&function(e){let{motionValue:t,name:i,repeatDelay:n,repeatType:r,damping:s,type:a}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return nb()&&i&&nx.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==r&&0!==s&&"inertia"!==a}(h)?new ny({...h,element:h.motionValue.owner.current}):new na(h);c.finished.then(()=>this.notifyFinished()).catch(el),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eb=!0,eP(),ew(),eb=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let nP=e=>null!==e,nS={type:"spring",stiffness:500,damping:25,restSpeed:10},nE=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),nT={type:"keyframes",duration:.8},nj={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nA=(e,{keyframes:t})=>t.length>2?nT:u.has(e)?e.startsWith("scale")?nE(t[1]):nS:nj,nM=(e,t,i,n={},r,s)=>a=>{let o=ia(n,e)||{},l=o.delay||n.delay||0,{elapsed:u=0}=n;u-=id(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-u,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:s?void 0:r};!function({when:e,delay:t,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&Object.assign(h,nA(e,h)),h.duration&&(h.duration=id(h.duration)),h.repeatDelay&&(h.repeatDelay=id(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(iu(h),0!==h.delay||(c=!0)),(eu.instantAnimations||eu.skipAnimations)&&(c=!0,iu(h),h.delay=0),h.allowFlatten=!o.type&&!o.ease,c&&!s&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},n){let r=e.filter(nP),s=t&&"loop"!==i&&t%2==1?0:r.length-1;return r[s]}(h.keyframes,o);if(void 0!==e){ep.update(()=>{h.onUpdate(e),h.onComplete()});return}}return o.isSync?new na(h):new nw(h)};function nR(e,t,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:a,...o}=t;n&&(s=n);let l=[],u=r&&e.animationState&&e.animationState.getState()[r];for(let t in o){let n=e.getValue(t,e.latestValues[t]??null),r=o[t];if(void 0===r||u&&function({protectedKeys:e,needsAnimating:t},i){let n=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,n}(u,t))continue;let a={delay:i,...ia(s||{},t)},h=n.get();if(void 0!==h&&!n.isAnimating&&!Array.isArray(r)&&r===h&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=e.props[ie];if(i){let e=window.MotionHandoffAnimation(i,t,ep);null!==e&&(a.startTime=e,c=!0)}}il(e,t),n.start(nM(t,n,r,e.shouldReduceMotion&&F.has(t)?{type:!1}:a,e,c));let d=n.animation;d&&l.push(d)}return a&&Promise.all(l).then(()=>{ep.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:n={},...r}=is(e,t)||{};for(let t in r={...r,...i}){var s;let i=io(s=r[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,ti(i))}}(e,a)})}),l}function nC(e,t,i,n=0,r=1){let s=Array.from(e).sort((e,t)=>e.sortNodePosition(t)).indexOf(t),a=e.size,o=(a-1)*n;return"function"==typeof i?i(s,a):1===r?s*n:o-s*n}function nk(e,t,i={}){let n=is(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:r=e.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(nR(e,n,i)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=r;return function(e,t,i=0,n=0,r=0,s=1,a){let o=[];for(let l of e.variantChildren)l.notify("AnimationStart",t),o.push(nk(l,t,{...a,delay:i+("function"==typeof n?0:n)+nC(e.variantChildren,l,n,r,s)}).then(()=>l.notify("AnimationComplete",t)));return Promise.all(o)}(e,t,n,s,a,o,i)}:()=>Promise.resolve(),{when:o}=r;if(!o)return Promise.all([s(),a(i.delay)]);{let[e,t]="beforeChildren"===o?[s,a]:[a,s];return e().then(()=>t())}}function nN(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let n=0;n<i;n++)if(t[n]!==e[n])return!1;return!0}let nD=tx.length,nO=[...tv].reverse(),n_=tv.length;function nV(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nL(){return{animate:nV(!0),whileInView:nV(),whileHover:nV(),whileTap:nV(),whileDrag:nV(),whileFocus:nV(),exit:nV()}}class nI{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nF extends nI{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>nk(e,t,i)));else if("string"==typeof t)n=nk(e,t,i);else{let r="function"==typeof t?is(e,t,i.custom):t;n=Promise.all(nR(e,r,i))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=nL(),n=!0,r=t=>(i,n)=>{let r=is(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(r){let{transition:e,transitionEnd:t,...n}=r;i={...i,...n,...t}}return i};function s(s){let{props:a}=e,o=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<nD;e++){let n=tx[e],r=t.props[n];(ty(r)||!1===r)&&(i[n]=r)}return i}(e.parent)||{},l=[],u=new Set,h={},c=1/0;for(let t=0;t<n_;t++){var d;let p=nO[t],f=i[p],m=void 0!==a[p]?a[p]:o[p],g=ty(m),y=p===s?f.isActive:null;!1===y&&(c=t);let v=m===o[p]&&m!==a[p]&&g;if(v&&n&&e.manuallyAnimateOnMount&&(v=!1),f.protectedKeys={...h},!f.isActive&&null===y||!m&&!f.prevProp||tg(m)||"boolean"==typeof m)continue;let x=(d=f.prevProp,"string"==typeof m?m!==d:!!Array.isArray(m)&&!nN(m,d)),b=x||p===s&&f.isActive&&!v&&g||t>c&&g,w=!1,P=Array.isArray(m)?m:[m],S=P.reduce(r(p),{});!1===y&&(S={});let{prevResolvedValues:E={}}=f,T={...E,...S},j=t=>{b=!0,u.has(t)&&(w=!0,u.delete(t)),f.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in T){let t=S[e],i=E[e];if(!h.hasOwnProperty(e))(io(t)&&io(i)?nN(t,i):t===i)?void 0!==t&&u.has(e)?j(e):f.protectedKeys[e]=!0:null!=t?j(e):u.add(e)}f.prevProp=m,f.prevResolvedValues=S,f.isActive&&(h={...h,...S}),n&&e.blockInitialAnimation&&(b=!1);let A=v&&x,M=!A||w;b&&M&&l.push(...P.map(t=>{let i={type:p};if("string"==typeof t&&n&&!A&&e.manuallyAnimateOnMount&&e.parent){let{parent:n}=e,r=is(n,t);if(n.enteringChildren&&r){let{delayChildren:t}=r.transition||{};i.delay=nC(n.enteringChildren,e,t)}}return{animation:t,options:i}}))}if(u.size){let t={};if("boolean"!=typeof a.initial){let i=is(e,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(t.transition=i.transition)}u.forEach(i=>{let n=e.getBaseTarget(i),r=e.getValue(i);r&&(r.liveStyle=!0),t[i]=n??null}),l.push({animation:t})}let p=!!l.length;return n&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(p=!1),n=!1,p?t(l):Promise.resolve()}return{animateChanges:s,setActive:function(t,n){if(i[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,n)),i[t].isActive=n;let r=s(t);for(let e in i)i[e].protectedKeys={};return r},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=nL(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();tg(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nB=0;class n$ extends nI{constructor(){super(...arguments),this.id=nB++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let nU={x:!1,y:!1};function nz(e,t,i,n={passive:!0}){return e.addEventListener(t,i,n),()=>e.removeEventListener(t,i)}let nW=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function nH(e){return{point:{x:e.pageX,y:e.pageY}}}let nX=e=>t=>nW(t)&&e(t,nH(t));function nq(e,t,i,n){return nz(e,t,nX(i),n)}function nY(e){return e.max-e.min}function nG(e,t,i,n=.5){e.origin=n,e.originPoint=A(t.min,t.max,e.origin),e.scale=nY(i)/nY(t),e.translate=A(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nK(e,t,i,n){nG(e.x,t.x,i.x,n?n.originX:void 0),nG(e.y,t.y,i.y,n?n.originY:void 0)}function nZ(e,t,i){e.min=i.min+t.min,e.max=e.min+nY(t)}function nQ(e,t,i){e.min=t.min-i.min,e.max=e.min+nY(t)}function nJ(e,t,i){nQ(e.x,t.x,i.x),nQ(e.y,t.y,i.y)}function n0(e){return[e("x"),e("y")]}let n1=({current:e})=>e?e.ownerDocument.defaultView:null,n2=(e,t)=>Math.abs(e-t);class n5{constructor(e,t,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=n6(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(n2(e.x,t.x)**2+n2(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!i)return;let{point:n}=e,{timestamp:r}=em;this.history.push({...n,timestamp:r});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=n3(t,this.transformPagePoint),ep.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=n6("pointercancel"===e.type?this.lastMoveEventInfo:n3(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,s),n&&n(e,s)},!nW(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let a=n3(nH(e),this.transformPagePoint),{point:o}=a,{timestamp:l}=em;this.history=[{...o,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,n6(a,this.history)),this.removeListeners=ic(nq(this.contextWindow,"pointermove",this.handlePointerMove),nq(this.contextWindow,"pointerup",this.handlePointerUp),nq(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),ef(this.updatePoint)}}function n3(e,t){return t?{point:t(e.point)}:e}function n4(e,t){return{x:e.x-t.x,y:e.y-t.y}}function n6({point:e},t){return{point:e,delta:n4(e,n8(t)),offset:n4(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,n=null,r=n8(e);for(;i>=0&&(n=e[i],!(r.timestamp-n.timestamp>id(.1)));)i--;if(!n)return{x:0,y:0};let s=ip(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let a={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function n8(e){return e[e.length-1]}function n9(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function n7(e,t){let i=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,n]=[n,i]),{min:i,max:n}}function re(e,t,i){return{min:rt(e,t),max:rt(e,i)}}function rt(e,t){return"number"==typeof e?e:e[t]||0}let ri=new WeakMap;class rn{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tc(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new n5(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(nH(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===i||"y"===i?nU[i]?null:(nU[i]=!0,()=>{nU[i]=!1}):nU.x||nU.y?null:(nU.x=nU.y=!0,()=>{nU.x=nU.y=!1}),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),n0(e=>{let t=this.getAxisMotionValue(e).get()||0;if(X.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[e];n&&(t=nY(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),r&&ep.postRender(()=>r(e,t)),il(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>n0(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,distanceThreshold:i,contextWindow:n1(this.visualElement)})}stop(e,t){let i=e||this.latestPointerEvent,n=t||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:a}=this.getProps();a&&ep.postRender(()=>a(i,n))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:n}=this.getProps();if(!i||!rr(e,n,this.currentDirection))return;let r=this.getAxisMotionValue(e),s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:i},n){return void 0!==t&&e<t?e=n?A(t,e,n.min):Math.max(e,t):void 0!==i&&e>i&&(e=n?A(i,e,n.max):Math.min(e,i)),e}(s,this.constraints[e],this.elastic[e])),r.set(s)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&t7(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:n,right:r}){return{x:n9(e.x,i,r),y:n9(e.y,t,n)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:re(e,"left","right"),y:re(e,"top","bottom")}}(t),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&n0(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!t7(t))return!1;let n=t.current;et(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(e,t,i){let n=I(e,i),{scroll:r}=t;return r&&(_(n.x,r.offset.x),_(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),a={x:n7((e=r.layout.layoutBox).x,s.x),y:n7(e.y,s.y)};if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=j(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(n0(a=>{if(!rr(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return il(this.visualElement,e),i.start(nM(e,i,0,t,this.visualElement,!1))}stopAnimation(){n0(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){n0(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){n0(t=>{let{drag:i}=this.getProps();if(!rr(t,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(t);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[t];r.set(e[t]-A(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!t7(t)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};n0(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();n[e]=function(e,t){let i=.5,n=nY(e),r=nY(t);return r>n?i=i6(t.min,t.max-n,e.min):n>r&&(i=i6(e.min,e.max-r,t.min)),B(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),n0(t=>{if(!rr(t,e,null))return;let i=this.getAxisMotionValue(t),{min:r,max:s}=this.constraints[t];i.set(A(r,s,n[t]))})}addListeners(){if(!this.visualElement.current)return;ri.set(this.visualElement,this);let e=nq(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();t7(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),ep.read(t);let r=nz(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(n0(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),n(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:a}}}function rr(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class rs extends nI{constructor(e){super(e),this.removeGroupControls=el,this.removeListeners=el,this.controls=new rn(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||el}unmount(){this.removeGroupControls(),this.removeListeners()}}let ra=e=>(t,i)=>{e&&ep.postRender(()=>e(t,i))};class ro extends nI{constructor(){super(...arguments),this.removePointerDownListener=el}onPointerDown(e){this.session=new n5(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:n1(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:ra(e),onStart:ra(t),onMove:i,onEnd:(e,t)=>{delete this.session,n&&ep.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=nq(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let rl={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ru(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let rh={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!q.test(e))return e;e=parseFloat(e)}let i=ru(e,t.target.x),n=ru(e,t.target.y);return`${i}% ${n}%`}},rc=!1;class rd extends o.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=e;(function(e){for(let t in e)tk[t]=e[t],P(t)&&(tk[t].isCSSVariable=!0)})(rf),r&&(t.group&&t.group.add(r),i&&i.register&&n&&i.register(r),rc&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),rl.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,rc=!0,n||e.layoutDependency!==t||void 0===t||e.isPresent!==r?s.willUpdate():this.safeToRemove(),e.isPresent===r||(r?s.promote():s.relegate()||ep.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ts.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:n}=e;rc=!0,n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rp(e){let[t,i]=function(e=!0){let t=(0,o.useContext)(t2);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=t,s=(0,o.useId)();(0,o.useEffect)(()=>{if(e)return r(s)},[e]);let a=(0,o.useCallback)(()=>e&&n&&n(s),[s,n,e]);return!i&&n?[!1,a]:[!0]}(),n=(0,o.useContext)(tH);return(0,a.jsx)(rd,{...e,layoutGroup:n,switchLayoutGroup:(0,o.useContext)(it),isPresent:t,safeToRemove:i})}let rf={borderRadius:{...rh,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rh,borderTopRightRadius:rh,borderBottomLeftRadius:rh,borderBottomRightRadius:rh,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let n=eW.parse(e);if(n.length>5)return e;let r=eW.createTransformer(e),s="number"!=typeof n[0]?1:0,a=i.x.scale*t.x,o=i.y.scale*t.y;n[0+s]/=a,n[1+s]/=o;let l=A(a,o,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};function rm(e){return"object"==typeof e&&null!==e}function rg(e){return rm(e)&&"ownerSVGElement"in e}let ry=(e,t)=>e.depth-t.depth;class rv{constructor(){this.children=[],this.isDirty=!1}add(e){e4(this.children,e),this.isDirty=!0}remove(e){e6(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(ry),this.isDirty=!1,this.children.forEach(e)}}let rx=["TopLeft","TopRight","BottomLeft","BottomRight"],rb=rx.length,rw=e=>"string"==typeof e?parseFloat(e):e,rP=e=>"number"==typeof e||q.test(e);function rS(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rE=rj(0,.5,i0),rT=rj(.5,.95,el);function rj(e,t,i){return n=>n<e?0:n>t?1:i(i6(e,t,n))}function rA(e,t){e.min=t.min,e.max=t.max}function rM(e,t){rA(e.x,t.x),rA(e.y,t.y)}function rR(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rC(e,t,i,n,r){return e-=t,e=n+1/i*(e-n),void 0!==r&&(e=n+1/r*(e-n)),e}function rk(e,t,[i,n,r],s,a){!function(e,t=0,i=1,n=.5,r,s=e,a=e){if(X.test(t)&&(t=parseFloat(t),t=A(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=A(s.min,s.max,n);e===s&&(o-=t),e.min=rC(e.min,t,i,o,r),e.max=rC(e.max,t,i,o,r)}(e,t[i],t[n],t[r],t.scale,s,a)}let rN=["x","scaleX","originX"],rD=["y","scaleY","originY"];function rO(e,t,i,n){rk(e.x,t,rN,i?i.x:void 0,n?n.x:void 0),rk(e.y,t,rD,i?i.y:void 0,n?n.y:void 0)}function r_(e){return 0===e.translate&&1===e.scale}function rV(e){return r_(e.x)&&r_(e.y)}function rL(e,t){return e.min===t.min&&e.max===t.max}function rI(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rF(e,t){return rI(e.x,t.x)&&rI(e.y,t.y)}function rB(e){return nY(e.x)/nY(e.y)}function r$(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rU{constructor(){this.members=[]}add(e){e4(this.members,e),e.scheduleRender()}remove(e){if(e6(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rz={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rW=["","X","Y","Z"],rH=0;function rX(e,t,i,n){let{latestValues:r}=t;r[e]&&(i[e]=r[e],t.setStaticValue(e,0),n&&(n[e]=0))}function rq({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(e={},i=t?.()){this.id=rH++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ec.value&&(rz.nodes=rz.calculatedTargetDeltas=rz.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(r5),this.nodes.forEach(r3),this.nodes.forEach(rZ),ec.addProjectionMetrics&&ec.addProjectionMetrics(rz)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rv)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new e8),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rg(t)&&!(rg(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),e){let i;let n=0,r=()=>this.root.updateBlockedByResize=!1;ep.read(()=>{n=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==n&&(n=e,this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=e3.now(),n=({timestamp:t})=>{let r=t-i;r>=250&&(ef(n),e(r-250))};return ep.setup(n,!0),()=>ef(n)}(r,250),rl.hasAnimatedSinceResize&&(rl.hasAnimatedSinceResize=!1,this.nodes.forEach(r2)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||se,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),l=!this.targetLayout||!rF(this.targetLayout,n),u=!t&&i;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...ia(s,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||r2(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ef(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r4),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let n=i.props[ie];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",ep,!(e||i))}let{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&e(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rJ);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(r0);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(r1),this.nodes.forEach(rY),this.nodes.forEach(rG)):this.nodes.forEach(r0),this.clearAllSnapshots();let e=e3.now();em.delta=B(0,1e3/60,e-em.timestamp),em.timestamp=e,em.isProcessing=!0,eg.update.process(em),eg.preRender.process(em),eg.render.process(em),em.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ts.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rQ),this.sharedNodes.forEach(r6)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ep.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ep.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||nY(this.snapshot.measuredBox.x)||nY(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tc(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rV(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||C(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),n=this.removeElementScroll(i);return e&&(n=this.removeTransform(n)),sn((t=n).x),sn(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return tc();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ss))){let{scroll:e}=this.root;e&&(_(t.x,e.offset.x),_(t.y,e.offset.y))}return t}removeElementScroll(e){let t=tc();if(rM(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&rM(t,e),_(t.x,r.offset.x),_(t.y,r.offset.y))}return t}applyTransform(e,t=!1){let i=tc();rM(i,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&L(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),C(n.latestValues)&&L(i,n.latestValues)}return C(this.latestValues)&&L(i,this.latestValues),i}removeTransform(e){let t=tc();rM(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!C(i.latestValues))continue;R(i.latestValues)&&i.updateSnapshot();let n=tc();rM(n,i.measurePageBox()),rO(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return C(this.latestValues)&&rO(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==em.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=em.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tc(),this.relativeTargetOrigin=tc(),nJ(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rM(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=tc(),this.targetWithTransforms=tc()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,nZ(s.x,a.x,o.x),nZ(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rM(this.target,this.layout.layoutBox),O(this.target,this.targetDelta)):rM(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tc(),this.relativeTargetOrigin=tc(),nJ(this.relativeTargetOrigin,this.target,e.target),rM(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ec.value&&rz.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||R(this.parent.latestValues)||k(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===em.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;rM(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;(function(e,t,i,n=!1){let r,s;let a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){s=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&L(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,O(e,s)),n&&C(r.latestValues)&&L(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=tc());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rR(this.prevProjectionDelta.x,this.projectionDelta.x),rR(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nK(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&r$(this.projectionDelta.x,this.prevProjectionDelta.x)&&r$(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),ec.value&&rz.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tu(),this.projectionDelta=tu(),this.projectionDeltaWithTransform=tu()}setAnimationOrigin(e,t=!1){let i;let n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},a=tu();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=tc(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(r7));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(r8(a.x,e.x,n),r8(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,f;nJ(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,r9(p.x,f.x,o.x,n),r9(p.y,f.y,o.y,n),i&&(u=this.relativeTarget,d=i,rL(u.x,d.x)&&rL(u.y,d.y))&&(this.isProjectionDirty=!1),i||(i=tc()),rM(i,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,i,n,r,s){r?(e.opacity=A(0,i.opacity??1,rE(n)),e.opacityExit=A(t.opacity??1,0,rT(n))):s&&(e.opacity=A(t.opacity??1,i.opacity??1,n));for(let r=0;r<rb;r++){let s=`border${rx[r]}Radius`,a=rS(t,s),o=rS(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||rP(a)===rP(o)?(e[s]=Math.max(A(rw(a),rw(o),n),0),(X.test(o)||X.test(a))&&(e[s]+="%")):e[s]=o)}(t.rotate||i.rotate)&&(e.rotate=A(t.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ef(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ep.update(()=>{rl.hasAnimatedSinceResize=!0,im.layout++,this.motionValue||(this.motionValue=ti(0)),this.currentAnimation=function(e,t,i){let n=e2(e)?e:ti(e);return n.start(nM("",n,t,i)),n.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{im.layout--},onComplete:()=>{im.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:n,latestValues:r}=e;if(t&&i&&n){if(this!==e&&this.layout&&n&&sr(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||tc();let t=nY(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let n=nY(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+n}rM(t,i),L(t,r),nK(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rU),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let n={};i.z&&rX("z",e,n,this.animationValues);for(let t=0;t<rW.length;t++)rX(`rotate${rW[t]}`,e,n,this.animationValues),rX(`skew${rW[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=t3(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=t3(t?.pointerEvents)||""),this.hasProjected&&!C(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1);return}e.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(e,t,i){let n="",r=e.x.translate/t.x,s=e.y.translate/t.y,a=i?.z||0;if((r||s||a)&&(n=`translate3d(${r}px, ${s}px, ${a}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:s,skewX:a,skewY:o}=i;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),e.transform=s;let{x:a,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*a.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,tk){if(void 0===r[t])continue;let{correct:i,applyTo:a,isCSSVariable:o}=tk[t],l="none"===s?r[t]:i(r[t],n);if(a){let t=a.length;for(let i=0;i<t;i++)e[a[i]]=l}else o?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=n===this?t3(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rJ),this.root.sharedNodes.clear()}}}function rY(e){e.updateLayout()}function rG(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=e.layout,{animationType:r}=e.options,s=t.source!==e.layout.source;"size"===r?n0(e=>{let n=s?t.measuredBox[e]:t.layoutBox[e],r=nY(n);n.min=i[e].min,n.max=n.min+r}):sr(r,t.layoutBox,i)&&n0(n=>{let r=s?t.measuredBox[n]:t.layoutBox[n],a=nY(i[n]);r.max=r.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=tu();nK(a,i,t.layoutBox);let o=tu();s?nK(o,e.applyTransform(n,!0),t.measuredBox):nK(o,i,t.layoutBox);let l=!rV(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let a=tc();nJ(a,t.layoutBox,r.layoutBox);let o=tc();nJ(o,i,s.layoutBox),rF(a,o)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rK(e){ec.value&&rz.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rZ(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rQ(e){e.clearSnapshot()}function rJ(e){e.clearMeasurements()}function r0(e){e.isLayoutDirty=!1}function r1(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function r2(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function r5(e){e.resolveTargetDelta()}function r3(e){e.calcProjection()}function r4(e){e.resetSkewAndRotation()}function r6(e){e.removeLeadSnapshot()}function r8(e,t,i){e.translate=A(t.translate,0,i),e.scale=A(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function r9(e,t,i,n){e.min=A(t.min,i.min,n),e.max=A(t.max,i.max,n)}function r7(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let se={duration:.45,ease:[.4,0,.1,1]},st=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),si=st("applewebkit/")&&!st("chrome/")?Math.round:el;function sn(e){e.min=si(e.min),e.max=si(e.max)}function sr(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rB(t)-rB(i)))}function ss(e){return e!==e.root&&e.scroll?.wasRoot}let sa=rq({attachResizeListener:(e,t)=>nz(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),so={current:void 0},sl=rq({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!so.current){let e=new sa({});e.mount(window),e.setOptions({layoutScroll:!0}),so.current=e}return so.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function su(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let n=document;t&&(n=t.current);let r=i?.[e]??n.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}function sh(e,t){let i=su(e),n=new AbortController;return[i,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function sc(e){return!("touch"===e.pointerType||nU.x||nU.y)}function sd(e,t,i){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&ep.postRender(()=>r(t,nH(t)))}class sp extends nI{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[n,r,s]=sh(e,i),a=e=>{if(!sc(e))return;let{target:i}=e,n=t(i,e);if("function"!=typeof n||!i)return;let s=e=>{sc(e)&&(n(e),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(e=>{e.addEventListener("pointerenter",a,r)}),s}(e,(e,t)=>(sd(this.node,t,"Start"),e=>sd(this.node,e,"End"))))}unmount(){}}class sf extends nI{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ic(nz(this.node.current,"focus",()=>this.onFocus()),nz(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function sm(e){return rm(e)&&"offsetHeight"in e}let sg=(e,t)=>!!t&&(e===t||sg(e,t.parentElement)),sy=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),sv=new WeakSet;function sx(e){return t=>{"Enter"===t.key&&e(t)}}function sb(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let sw=(e,t)=>{let i=e.currentTarget;if(!i)return;let n=sx(()=>{if(sv.has(i))return;sb(i,"down");let e=sx(()=>{sb(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>sb(i,"cancel"),t)});i.addEventListener("keydown",n,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),t)};function sP(e){return nW(e)&&!(nU.x||nU.y)}function sS(e,t,i){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&ep.postRender(()=>r(t,nH(t)))}class sE extends nI{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[n,r,s]=sh(e,i),a=e=>{let n=e.currentTarget;if(!sP(e))return;sv.add(n);let s=t(n,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),sv.has(n)&&sv.delete(n),sP(e)&&"function"==typeof s&&s(e,{success:t})},o=e=>{a(e,n===window||n===document||i.useGlobalTarget||sg(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return n.forEach(e=>{(i.useGlobalTarget?window:e).addEventListener("pointerdown",a,r),sm(e)&&(e.addEventListener("focus",e=>sw(e,r)),sy.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(sS(this.node,t,"Start"),(e,{success:t})=>sS(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sT=new WeakMap,sj=new WeakMap,sA=e=>{let t=sT.get(e.target);t&&t(e)},sM=e=>{e.forEach(sA)},sR={some:0,all:1};class sC extends nI{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:n="some",once:r}=e,s={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:sR[n]};return function(e,t,i){let n=function({root:e,...t}){let i=e||document;sj.has(i)||sj.set(i,{});let n=sj.get(i),r=JSON.stringify(t);return n[r]||(n[r]=new IntersectionObserver(sM,{root:e,...t})),n[r]}(t);return sT.set(e,i),n.observe(e),()=>{sT.delete(e),n.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=t?i:n;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let sk=function(e,t){if("undefined"==typeof Proxy)return ir;let i=new Map,n=(i,n)=>ir(i,n,e,t);return new Proxy((e,t)=>n(e,t),{get:(r,s)=>"create"===s?n:(i.has(s)||i.set(s,ir(s,void 0,e,t)),i.get(s))})}({animation:{Feature:nF},exit:{Feature:n$},inView:{Feature:sC},tap:{Feature:sE},focus:{Feature:sf},hover:{Feature:sp},pan:{Feature:ro},drag:{Feature:rs,ProjectionNode:sl,MeasureLayout:rp},layout:{ProjectionNode:sl,MeasureLayout:rp}},(e,t)=>tW(e)?new tU(t):new tO(t,{allowProjection:e!==o.Fragment}));function sN(){let[e,t]=(0,o.useState)(null),[i,n]=(0,o.useState)({x:0,y:0});return(0,a.jsxs)("section",{className:"min-h-screen relative overflow-hidden py-24 md:py-0",children:[(0,a.jsxs)("div",{className:"absolute inset-0",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-[url('/grid.svg')] opacity-10"}),(0,a.jsx)("div",{className:"absolute inset-0 pointer-events-none transition-opacity duration-300",style:{background:`radial-gradient(600px circle at ${i.x}px ${i.y}px,
              rgba(20, 184, 166, 0.25) 0%,
              rgba(20, 184, 166, 0.15) 25%,
              rgba(20, 184, 166, 0.08) 50%,
              transparent 70%)`}})]}),(0,a.jsxs)("div",{className:"relative z-10 min-h-screen flex flex-col items-center justify-center px-4 pt-8 md:pt-0",children:[(0,a.jsx)(sk.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-8 md:mb-12",children:(0,a.jsxs)("div",{className:"space-y-3 md:space-y-4 mb-6 md:mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-7xl font-bold text-transparent bg-clip-text text-white",children:"Ben Basil Tomy"}),(0,a.jsx)("h2",{className:"text-2xl md:text-4xl font-bold text-gray-300",children:"Software Engineer"}),(0,a.jsx)("p",{className:"text-lg md:text-xl text-gray-400 max-w-2xl mx-auto",children:"I craft end-to-end solutions with 2+ years of experience building scalable applications."})]})}),(0,a.jsx)("div",{className:"w-full max-w-5xl mx-auto relative px-2 md:px-4",children:(0,a.jsx)(sk.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800 p-4 md:p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8",children:[(0,a.jsxs)("div",{className:`p-4 md:p-6 rounded-lg transition-colors border-2 ${"frontend"===e?"bg-teal-500/20 border-teal-500/50":"bg-gray-800/50 hover:bg-gray-800/80 border-transparent"}`,onMouseEnter:()=>t("frontend"),onMouseLeave:()=>t(null),children:[(0,a.jsx)("h3",{className:"text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400",children:"Frontend Development"}),(0,a.jsxs)("ul",{className:"space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"Modern Angular Architectures"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"Performance Optimization"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"Performance Optimization"]})]})]}),(0,a.jsxs)("div",{className:`p-4 md:p-6 rounded-lg transition-colors border-2 ${"backend"===e?"bg-teal-500/20 border-teal-500/50":"bg-gray-800/50 hover:bg-gray-800/80 border-transparent"}`,onMouseEnter:()=>t("backend"),onMouseLeave:()=>t(null),children:[(0,a.jsx)("h3",{className:"text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400",children:"Backend Development"}),(0,a.jsxs)("ul",{className:"space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"API Design & Development"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"Database Architecture"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"Real-time Systems"]})]})]}),(0,a.jsxs)("div",{className:`p-4 md:p-6 rounded-lg transition-colors border-2 ${"devops"===e?"bg-teal-500/20 border-teal-500/50":"bg-gray-800/50 hover:bg-gray-800/80 border-transparent"}`,onMouseEnter:()=>t("devops"),onMouseLeave:()=>t(null),children:[(0,a.jsx)("h3",{className:"text-lg md:text-xl font-semibold mb-3 md:mb-4 text-teal-400",children:"DevOps & Cloud"}),(0,a.jsxs)("ul",{className:"space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-400",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"AWS Infrastructure"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"Docker"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-teal-500 rounded-full"}),"Scalable Architecture"]})]})]})]})})})]})]})}function sD(){return(0,a.jsx)("section",{className:"py-20 px-4",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto text-center",children:(0,a.jsxs)(sk.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"space-y-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold",children:"Let's Build Something Amazing"}),(0,a.jsx)("p",{className:"text-gray-400 max-w-2xl mx-auto",children:"Looking for a software engineer who can architect and implement complete solutions? Let's discuss your project."}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-gray-400",children:[(0,a.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,a.jsx)("span",{children:"India"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"px-8 py-3 bg-teal-500 rounded-lg font-medium hover:opacity-90 transition-opacity inline-flex items-center justify-center gap-2",children:[(0,a.jsxs)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,a.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,a.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),"Get in Touch"]}),(0,a.jsxs)("a",{href:"/resume.pdf",target:"_blank",className:"px-8 py-3 bg-black border border-gray-800 rounded-lg font-medium hover:bg-gray-900 transition-colors inline-flex items-center justify-center gap-2",children:[(0,a.jsxs)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z",clipRule:"evenodd"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M8 11a1 1 0 100 2h4a1 1 0 100-2H8zm0-4a1 1 0 100 2h4a1 1 0 100-2H8z",clipRule:"evenodd"})]}),"View Resume"]})]}),(0,a.jsxs)("div",{className:"flex justify-center gap-6",children:[(0,a.jsx)("a",{href:"https://github.com/BenBasilTomy",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})})}),(0,a.jsx)("a",{href:"https://www.linkedin.com/in/ben-basil-tomy-5533b9218/",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"})})})]})]})})})}function sO(e,t){let i;let n=()=>{let{currentTime:n}=t,r=(null===n?0:n.value)/100;i!==r&&e(r),i=r};return ep.preUpdate(n,!0),()=>ef(n)}let s_=new WeakMap,sV=(e,t,i)=>(n,r)=>r&&r[0]?r[0][e+"Size"]:rg(n)&&"getBBox"in n?n.getBBox()[t]:n[i],sL=sV("inline","width","offsetWidth"),sI=sV("block","height","offsetHeight");function sF({target:e,borderBoxSize:t}){s_.get(e)?.forEach(i=>{i(e,{get width(){return sL(e,t)},get height(){return sI(e,t)}})})}function sB(e){e.forEach(sF)}let s$=new Set,sU=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),sz=()=>({time:0,x:sU(),y:sU()}),sW={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function sH(e,t,i,n){let r=i[t],{length:s,position:a}=sW[t],o=r.current,l=i.time;r.current=e[`scroll${a}`],r.scrollLength=e[`scroll${s}`]-e[`client${s}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=i6(0,r.scrollLength,r.current);let u=n-l;r.velocity=u>50?0:e9(r.current-o,u)}let sX={start:0,center:.5,end:1};function sq(e,t,i=0){let n=0;if(e in sX&&(e=sX[e]),"string"==typeof e){let t=parseFloat(e);e.endsWith("px")?n=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?n=t/100*document.documentElement.clientWidth:e.endsWith("vh")?n=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(n=t*e),i+n}let sY=[0,0],sG={All:[[0,0],[1,1]]},sK={x:0,y:0},sZ=new WeakMap,sQ=new WeakMap,sJ=new WeakMap,s0=e=>e===document.scrollingElement?window:e;function s1(e,{container:t=document.scrollingElement,...i}={}){if(!t)return el;let n=sJ.get(t);n||(n=new Set,sJ.set(t,n));let a=function(e,t,i,n={}){return{measure:t=>{(function(e,t=e,i){if(i.x.targetOffset=0,i.y.targetOffset=0,t!==e){let n=t;for(;n&&n!==e;)i.x.targetOffset+=n.offsetLeft,i.y.targetOffset+=n.offsetTop,n=n.offsetParent}i.x.targetLength=t===e?t.scrollWidth:t.clientWidth,i.y.targetLength=t===e?t.scrollHeight:t.clientHeight,i.x.containerLength=e.clientWidth,i.y.containerLength=e.clientHeight})(e,n.target,i),sH(e,"x",i,t),sH(e,"y",i,t),i.time=t,(n.offset||n.target)&&function(e,t,i){let{offset:n=sG.All}=i,{target:r=e,axis:s="y"}=i,a="y"===s?"height":"width",o=r!==e?function(e,t){let i={x:0,y:0},n=e;for(;n&&n!==t;)if(sm(n))i.x+=n.offsetLeft,i.y+=n.offsetTop,n=n.offsetParent;else if("svg"===n.tagName){let e=n.getBoundingClientRect(),t=(n=n.parentElement).getBoundingClientRect();i.x+=e.left-t.left,i.y+=e.top-t.top}else if(n instanceof SVGGraphicsElement){let{x:e,y:t}=n.getBBox();i.x+=e,i.y+=t;let r=null,s=n.parentNode;for(;!r;)"svg"===s.tagName&&(r=s),s=n.parentNode;n=r}else break;return i}(r,e):sK,l=r===e?{width:e.scrollWidth,height:e.scrollHeight}:"getBBox"in r&&"svg"!==r.tagName?r.getBBox():{width:r.clientWidth,height:r.clientHeight},u={width:e.clientWidth,height:e.clientHeight};t[s].offset.length=0;let h=!t[s].interpolate,c=n.length;for(let e=0;e<c;e++){let i=function(e,t,i,n){let r=Array.isArray(e)?e:sY,s=0;return"number"==typeof e?r=[e,e]:"string"==typeof e&&(r=(e=e.trim()).includes(" ")?e.split(" "):[e,sX[e]?e:"0"]),sq(r[0],i,n)-sq(r[1],t)}(n[e],u[a],l[a],o[s]);h||i===t[s].interpolatorOffsets[e]||(h=!0),t[s].offset[e]=i}h&&(t[s].interpolate=i8(t[s].offset,i9(n),{clamp:!1}),t[s].interpolatorOffsets=[...t[s].offset]),t[s].progress=B(0,1,t[s].interpolate(t[s].current))}(e,i,n)},notify:()=>t(i)}}(t,e,sz(),i);if(n.add(a),!sZ.has(t)){let e=()=>{for(let e of n)e.measure(em.timestamp);ep.preUpdate(i)},i=()=>{for(let e of n)e.notify()},a=()=>ep.read(e);sZ.set(t,a);let o=s0(t);window.addEventListener("resize",a,{passive:!0}),t!==document.documentElement&&sQ.set(t,"function"==typeof t?(s$.add(t),s||(s=()=>{let e={get width(){return window.innerWidth},get height(){return window.innerHeight}};s$.forEach(t=>t(e))},window.addEventListener("resize",s)),()=>{s$.delete(t),s$.size||"function"!=typeof s||(window.removeEventListener("resize",s),s=void 0)}):function(e,t){r||"undefined"==typeof ResizeObserver||(r=new ResizeObserver(sB));let i=su(e);return i.forEach(e=>{let i=s_.get(e);i||(i=new Set,s_.set(e,i)),i.add(t),r?.observe(e)}),()=>{i.forEach(e=>{let i=s_.get(e);i?.delete(t),i?.size||r?.unobserve(e)})}}(t,a)),o.addEventListener("scroll",a,{passive:!0}),a()}let o=sZ.get(t);return ep.read(o,!1,!0),()=>{ef(o);let e=sJ.get(t);if(!e||(e.delete(a),e.size))return;let i=sZ.get(t);sZ.delete(t),i&&(s0(t).removeEventListener("scroll",i),sQ.get(t)?.(),window.removeEventListener("resize",i))}}let s2=new Map;function s5({source:e,container:t,...i}){let{axis:n}=i;e&&(t=e);let r=s2.get(t)??new Map;s2.set(t,r);let s=i.target??"self",a=r.get(s)??{},o=n+(i.offset??[]).join(",");return a[o]||(a[o]=!i.target&&nu()?new ScrollTimeline({source:t,axis:n}):function(e){let t={value:0},i=s1(i=>{t.value=100*i[e.axis].progress},e);return{currentTime:t,cancel:i}}({container:t,...i})),a[o]}let s3=()=>({scrollX:ti(0),scrollY:ti(0),scrollXProgress:ti(0),scrollYProgress:ti(0)}),s4=e=>!!e&&!e.current;function s6(e,t){let i=function(e){let t=t5(()=>ti(e)),{isStatic:i}=(0,o.useContext)(tq);if(i){let[,i]=(0,o.useState)(e);(0,o.useEffect)(()=>t.on("change",i),[])}return t}(t()),n=()=>i.set(t());return n(),ii(()=>{let t=()=>ep.preRender(n,!1,!0),i=e.map(e=>e.on("change",t));return()=>{i.forEach(e=>e()),ef(n)}}),i}function s8(e,t){let i=t5(()=>[]);return s6(e,()=>{i.length=0;let n=e.length;for(let t=0;t<n;t++)i[t]=e[t].get();return t(i)})}var s9=i(3864),s7=i.n(s9);function ae(){let[e,t]=(0,o.useState)(null),[i,n]=(0,o.useState)({x:0,y:0}),[r,s]=(0,o.useState)(!1),[l,u]=(0,o.useState)({x:0,y:0}),[h,c]=(0,o.useState)(!1),d=(0,o.useRef)(null),p=(0,o.useRef)(null),{scrollYProgress:f}=function({container:e,target:t,...i}={}){let n=t5(s3),r=(0,o.useRef)(null),s=(0,o.useRef)(!1),a=(0,o.useCallback)(()=>(r.current=function(e,{axis:t="y",container:i=document.scrollingElement,...n}={}){if(!i)return el;let r={axis:t,container:i,...n};return"function"==typeof e?2===e.length?s1(t=>{e(t[r.axis].progress,t)},r):sO(e,s5(r)):function(e,t){let i=s5(t);return e.attachTimeline({timeline:t.target?void 0:i,observe:e=>(e.pause(),sO(t=>{e.time=e.duration*t},i))})}(e,r)}((e,{x:t,y:i})=>{n.scrollX.set(t.current),n.scrollXProgress.set(t.progress),n.scrollY.set(i.current),n.scrollYProgress.set(i.progress)},{...i,container:e?.current||void 0,target:t?.current||void 0}),()=>{r.current?.()}),[e,t,JSON.stringify(i.offset)]);return ii(()=>{if(s.current=!1,!(s4(e)||s4(t)))return a();s.current=!0},[a]),(0,o.useEffect)(()=>s.current?(et(!s4(e),"Container ref is defined but not hydrated","use-scroll-ref"),et(!s4(t),"Target ref is defined but not hydrated","use-scroll-ref"),a()):void 0,[a]),n}({target:d,offset:["start end","end start"]}),m=function(e,t,i,n){if("function"==typeof e)return function(e){te.current=[],e();let t=s6(te.current,e);return te.current=void 0,t}(e);let r="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),i=t?0:-1,n=e[0+i],r=i8(e[1+i],e[2+i],e[3+i]);return t?r(n):r}(t,i,void 0);return Array.isArray(e)?s8(e,r):s8([e],([e])=>r(e))}(f,[0,.5,1],[15,0,-5]);return(0,a.jsx)("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)(sk.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"text-3xl font-bold mb-16 text-center",children:"Experience"}),(0,a.jsx)("div",{className:"space-y-16",children:(0,a.jsxs)(sk.div,{ref:d,initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},onMouseMove:e=>{if(d.current){let t=d.current.getBoundingClientRect();u({x:e.clientX-t.left,y:e.clientY-t.top})}},onMouseEnter:()=>{c(!0)},onMouseLeave:()=>{c(!1)},style:{rotateX:m,transformPerspective:1e3,transformStyle:"preserve-3d",background:h?`radial-gradient(900px circle at ${l.x}px ${l.y}px, rgba(20, 184, 166, 0.08), rgba(17, 24, 39, 0.95) 50%)`:void 0},className:"relative bg-gray-900/95 backdrop-blur-md rounded-xl overflow-hidden border border-white/10 shadow-2xl transition-all duration-300",children:[(0,a.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-br from-white/8 via-transparent to-gray-900/30 pointer-events-none"}),h&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-xl opacity-60 pointer-events-none",style:{background:`radial-gradient(900px circle at ${l.x}px ${l.y}px, rgba(20, 184, 166, 0.25), transparent 50%)`,mask:"linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",maskComposite:"xor",WebkitMask:"linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",WebkitMaskComposite:"xor",padding:"2px"}}),h&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-xl opacity-25 pointer-events-none",style:{background:`radial-gradient(700px circle at ${l.x}px ${l.y}px, rgba(20, 184, 166, 0.3), transparent 60%)`,filter:"blur(40px)"}}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-xl shadow-inner pointer-events-none",style:{boxShadow:"inset 0 1px 0 0 rgba(255, 255, 255, 0.08), inset 0 -1px 0 0 rgba(0, 0, 0, 0.4)"}}),(0,a.jsxs)("div",{ref:p,className:"relative p-4 md:p-6 rounded-lg transition-all duration-300 bg-white/5 backdrop-blur-sm hover:bg-white/8",onMouseMove:e=>{if(p.current){let t=p.current.getBoundingClientRect();n({x:e.clientX-t.left,y:e.clientY-t.top})}},onMouseEnter:()=>{s(!0),t("job")},onMouseLeave:()=>{s(!1),t(null)},style:{background:r?`radial-gradient(600px circle at ${i.x}px ${i.y}px, rgba(20, 184, 166, 0.15), rgba(255, 255, 255, 0.05) 40%)`:void 0},children:[r&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-lg opacity-60 pointer-events-none",style:{background:`radial-gradient(600px circle at ${i.x}px ${i.y}px, rgba(20, 184, 166, 0.4), transparent 40%)`,mask:"linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",maskComposite:"xor",WebkitMask:"linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",WebkitMaskComposite:"xor",padding:"1px"}}),r&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-lg opacity-20 pointer-events-none",style:{background:`radial-gradient(400px circle at ${i.x}px ${i.y}px, rgba(20, 184, 166, 0.6), transparent 60%)`,filter:"blur(20px)"}}),(0,a.jsx)("div",{className:"relative z-10 p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Cavli Wireless"}),(0,a.jsx)("p",{className:"text-gray-400",children:"A high-performance e-commerce solution handling 100K+ daily transactions with real-time inventory and ML-powered recommendations."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-teal-400 mb-3",children:"Frontend Architecture"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm text-gray-400",children:[(0,a.jsx)("li",{children:"• Next.js Server Components"}),(0,a.jsx)("li",{children:"• Real-time Cart & Inventory"}),(0,a.jsx)("li",{children:"• Stripe Payment Integration"}),(0,a.jsx)("li",{children:"• PWA with Offline Support"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-teal-400 mb-3",children:"Backend Systems"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm text-gray-400",children:[(0,a.jsx)("li",{children:"• Node.js Microservices"}),(0,a.jsx)("li",{children:"• Redis Caching Layer"}),(0,a.jsx)("li",{children:"• Kafka Event Streaming"}),(0,a.jsx)("li",{children:"• Elasticsearch Product Search"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-teal-400",children:"Key Achievements"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm text-gray-400",children:[(0,a.jsx)("li",{children:"• 99.99% Uptime with Blue-Green Deployment"}),(0,a.jsx)("li",{children:"• 300ms Average API Response Time"}),(0,a.jsx)("li",{children:"• 45% Reduction in Infrastructure Costs"})]})]})]}),(0,a.jsx)("div",{className:"bg-black/30 rounded-xl p-6",children:(0,a.jsx)(s7(),{src:"/hubble-image.webp",alt:"A description of the image",width:800,height:300})})]})}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-2 md:gap-3 mb-6 md:mb-8",children:[(0,a.jsx)("span",{className:"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm",children:"Javascript"}),(0,a.jsx)("span",{className:"px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm",children:"Express.js"}),(0,a.jsx)("span",{className:"px-3 md:px-4 py-1.5 md:py-2 bg-teal-500/10 rounded-full text-teal-400 text-xs md:text-sm",children:"Node.js"}),(0,a.jsx)("span",{className:"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm",children:"Mongo DB"}),(0,a.jsx)("span",{className:"px-3 md:px-4 py-1.5 md:py-2 bg-purple-500/10 rounded-full text-teal-400 text-xs md:text-sm",children:"AWS"}),(0,a.jsx)("span",{className:"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm",children:"TypeScript"}),(0,a.jsx)("span",{className:"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm",children:"NATS"}),(0,a.jsx)("span",{className:"px-3 md:px-4 py-1.5 md:py-2 bg-blue-500/10 rounded-full text-teal-400 text-xs md:text-sm",children:"Docker"})]})]})]})})]})})}let at=["JavaScript","TypeScript","Node.js","Express.js","MongoDB","AWS","Docker","Redis","REST APIs","Angular","SASS","Git","Microservices","NATS","MQTT"],ai=[...at,...at];function an(){let[e,t]=(0,o.useState)(!1),i=(0,o.useRef)(null),n=(0,o.useRef)(null),[r,s]=(0,o.useState)(-1920);return(0,a.jsx)("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("div",{className:"mb-16"}),(0,a.jsxs)("div",{className:"relative overflow-hidden",ref:i,children:[(0,a.jsx)("div",{className:"absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-gray-950 to-transparent z-10 pointer-events-none"}),(0,a.jsx)("div",{className:"absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-gray-950 to-transparent z-10 pointer-events-none"}),(0,a.jsx)("div",{className:"overflow-hidden",onMouseEnter:()=>t(!0),onMouseLeave:()=>t(!1),children:(0,a.jsx)(sk.div,{ref:n,className:"flex gap-6 w-fit",animate:{x:e?void 0:[0,r]},transition:{x:{repeat:1/0,repeatType:"loop",duration:42,ease:"linear"}},children:ai.map((e,t)=>(0,a.jsx)(ar,{skill:e},`${e}-${t}`))})})]})]})})}function ar({skill:e}){let[t,i]=(0,o.useState)(!1);return(0,a.jsxs)(sk.div,{className:"relative group cursor-pointer",onMouseEnter:()=>i(!0),onMouseLeave:()=>i(!1),whileHover:{scale:1.05},transition:{duration:.2},children:[(0,a.jsx)("div",{className:`absolute inset-0 rounded-xl transition-opacity duration-300 ${t?"opacity-100":"opacity-0"}`,style:{background:"linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8))",padding:"2px"},children:(0,a.jsx)("div",{className:"w-full h-full bg-gray-900/90 rounded-xl"})}),(0,a.jsxs)("div",{className:"relative px-6 py-4 min-w-[140px] text-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"}),(0,a.jsx)("div",{className:"relative z-10",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-200 whitespace-nowrap",children:e})}),(0,a.jsx)("div",{className:`absolute inset-0 rounded-xl transition-opacity duration-300 ${t?"opacity-20":"opacity-0"}`,style:{background:"radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)"}})]})]})}function as(){return(0,a.jsxs)("main",{className:"min-h-screen bg-gradient-to-b from-gray-950 to-black text-white",children:[(0,a.jsx)(sN,{}),(0,a.jsx)(an,{}),(0,a.jsx)(ae,{}),(0,a.jsx)(sD,{})]})}},1902:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let n=i(5488),r=i(1063),s=i(5512),a=r._(i(8009)),o=n._(i(5740)),l=n._(i(9153)),u=i(2034),h=i(4653),c=i(8156);i(6831);let d=i(4055),p=n._(i(1628)),f=i(3727),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,i,n,r,s,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&r(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,r=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{r=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function y(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,a.forwardRef)((e,t)=>{let{src:i,srcSet:n,sizes:r,height:o,width:l,decoding:u,className:h,style:c,fetchPriority:d,placeholder:p,loading:m,unoptimized:v,fill:x,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:P,setShowAltText:S,sizesInput:E,onLoad:T,onError:j,...A}=e,M=(0,a.useCallback)(e=>{e&&(j&&(e.src=e.src),e.complete&&g(e,p,b,w,P,v,E))},[i,p,b,w,P,j,v,E]),R=(0,f.useMergedRef)(t,M);return(0,s.jsx)("img",{...A,...y(d),loading:m,width:l,height:o,decoding:u,"data-nimg":x?"fill":"1",className:h,style:c,sizes:r,srcSet:n,src:i,ref:R,onLoad:e=>{g(e.currentTarget,p,b,w,P,v,E)},onError:e=>{S(!0),"empty"!==p&&P(!0),j&&j(e)}})});function x(e){let{isAppRouter:t,imgAttributes:i}=e,n={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...y(i.fetchPriority)};return t&&o.default.preload?(o.default.preload(i.src,n),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...n},"__nimg-"+i.src+i.srcSet+i.sizes)})}let b=(0,a.forwardRef)((e,t)=>{let i=(0,a.useContext)(d.RouterContext),n=(0,a.useContext)(c.ImageConfigContext),r=(0,a.useMemo)(()=>{var e;let t=m||n||h.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),r=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:r,qualities:s}},[n]),{onLoad:o,onLoadingComplete:l}=e,f=(0,a.useRef)(o);(0,a.useEffect)(()=>{f.current=o},[o]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[y,b]=(0,a.useState)(!1),[w,P]=(0,a.useState)(!1),{props:S,meta:E}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:r,blurComplete:y,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v,{...S,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:f,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:P,sizesInput:e.sizes,ref:t}),E.priority?(0,s.jsx)(x,{isAppRouter:!i,imgAttributes:S}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3727:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(8009);function r(e,t){let i=(0,n.useRef)(()=>{}),r=(0,n.useRef)(()=>{});return(0,n.useMemo)(()=>e&&t?n=>{null===n?(i.current(),r.current()):(i.current=s(e,n),r.current=s(t,n))}:e||t,[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2782:(e,t,i)=>{"use strict";e.exports=i(8104).vendored.contexts.AmpContext},6302:(e,t,i)=>{"use strict";e.exports=i(8104).vendored.contexts.HeadManagerContext},8156:(e,t,i)=>{"use strict";e.exports=i(8104).vendored.contexts.ImageConfigContext},4055:(e,t,i)=>{"use strict";e.exports=i(8104).vendored.contexts.RouterContext},2677:(e,t)=>{"use strict";function i(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||i&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return i}})},2034:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),i(6831);let n=i(8337),r=i(4653);function s(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var i,o;let l,u,h,{src:c,sizes:d,unoptimized:p=!1,priority:f=!1,loading:m,className:g,quality:y,width:v,height:x,fill:b=!1,style:w,overrideSrc:P,onLoad:S,onLoadingComplete:E,placeholder:T="empty",blurDataURL:j,fetchPriority:A,decoding:M="async",layout:R,objectFit:C,objectPosition:k,lazyBoundary:N,lazyRoot:D,...O}=e,{imgConf:_,showAltText:V,blurComplete:L,defaultLoader:I}=t,F=_||r.imageConfigDefault;if("allSizes"in F)l=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),n=null==(i=F.qualities)?void 0:i.sort((e,t)=>e-t);l={...F,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===I)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let B=O.loader||I;delete O.loader,delete O.srcSet;let $="__next_img_default"in B;if($){if("custom"===l.loader)throw Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=B;B=t=>{let{config:i,...n}=t;return e(n)}}if(R){"fill"===R&&(b=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!d&&(d=t)}let U="",z=a(v),W=a(x);if((o=c)&&"object"==typeof o&&(s(o)||void 0!==o.src)){let e=s(c)?c.default:c;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(u=e.blurWidth,h=e.blurHeight,j=j||e.blurDataURL,U=e.src,!b){if(z||W){if(z&&!W){let t=z/e.width;W=Math.round(e.height*t)}else if(!z&&W){let t=W/e.height;z=Math.round(e.width*t)}}else z=e.width,W=e.height}}let H=!f&&("lazy"===m||void 0===m);(!(c="string"==typeof c?c:U)||c.startsWith("data:")||c.startsWith("blob:"))&&(p=!0,H=!1),l.unoptimized&&(p=!0),$&&!l.dangerouslyAllowSVG&&c.split("?",1)[0].endsWith(".svg")&&(p=!0);let X=a(y),q=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:C,objectPosition:k}:{},V?{}:{color:"transparent"},w),Y=L||"empty"===T?null:"blur"===T?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:z,heightInt:W,blurWidth:u,blurHeight:h,blurDataURL:j||"",objectFit:q.objectFit})+'")':'url("'+T+'")',G=Y?{backgroundSize:q.objectFit||"cover",backgroundPosition:q.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},K=function(e){let{config:t,src:i,unoptimized:n,width:r,quality:s,sizes:a,loader:o}=e;if(n)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,i){let{deviceSizes:n,allSizes:r}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(i);n)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:r.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>r.find(t=>t>=e)||r[r.length-1]))],kind:"x"}}(t,r,a),h=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,n)=>o({config:t,src:i,quality:s,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:o({config:t,src:i,quality:s,width:l[h]})}}({config:l,src:c,unoptimized:p,width:z,quality:X,sizes:d,loader:B});return{props:{...O,loading:H?"lazy":m,fetchPriority:A,width:z,height:W,decoding:M,className:g,style:{...q,...G},sizes:K.sizes,srcSet:K.srcSet,src:P||K.src},meta:{unoptimized:p,priority:f,placeholder:T,fill:b}}}},9153:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return m},defaultHead:function(){return c}});let n=i(5488),r=i(1063),s=i(5512),a=r._(i(8009)),o=n._(i(7440)),l=i(2782),u=i(6302),h=i(2677);function c(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}i(6831);let p=["name","httpEquiv","charSet","itemProp"];function f(e,t){let{inAmpMode:i}=t;return e.reduce(d,[]).reverse().concat(c(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,n={};return r=>{let s=!0,a=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){a=!0;let t=r.key.slice(r.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(r.type){case"title":case"base":t.has(r.type)?s=!1:t.add(r.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(r.props.hasOwnProperty(t)){if("charSet"===t)i.has(t)?s=!1:i.add(t);else{let e=r.props[t],i=n[t]||new Set;("name"!==t||!a)&&i.has(e)?s=!1:(i.add(e),n[t]=i)}}}}return s}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!i&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:n})})}let m=function(e){let{children:t}=e,i=(0,a.useContext)(l.AmpStateContext),n=(0,a.useContext)(u.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:f,headManager:n,inAmpMode:(0,h.isInAmpMode)(i),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8337:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:n,blurHeight:r,blurDataURL:s,objectFit:a}=e,o=n?40*n:t,l=r?40*r:i,u=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},4653:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return n}});let i=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},3864:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return o}});let n=i(5488),r=i(2034),s=i(1902),a=n._(i(1628));function o(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=s.Image},1628:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:n,width:r,quality:s}=e,a=s||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(n)+"&w="+r+"&q="+a+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),i.__next_img_default=!0;let n=i},7440:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=i(8009),r=()=>{},s=()=>{};function a(e){var t;let{headManager:i,reduceComponentsToState:a}=e;function o(){if(i&&i.mountedInstances){let t=n.Children.toArray(Array.from(i.mountedInstances).filter(Boolean));i.updateHead(a(t,e))}}return null==i||null==(t=i.mountedInstances)||t.add(e.children),o(),r(()=>{var t;return null==i||null==(t=i.mountedInstances)||t.add(e.children),()=>{var t;null==i||null==(t=i.mountedInstances)||t.delete(e.children)}}),r(()=>(i&&(i._pendingUpdate=o),()=>{i&&(i._pendingUpdate=o)})),s(()=>(i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null),()=>{i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null)})),null}},9611:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u,metadata:()=>l});var n=i(2740),r=i(7879),s=i.n(r),a=i(3298),o=i.n(a);i(2704);let l={title:"[Ben Basil Tomy] - Software Engineer",description:"Welcome to my portfolio! I am a passionate full-stack developer who bridges the gap between frontend and backend development. Specializing in creating complete web solutions, from beautiful user interfaces to robust server architectures.",keywords:["Full-Stack Developer","Software Engineer","Frontend Development","Backend Development","React","Next.js","Node.js","TypeScript","Database Design","API Development","Cloud Solutions","DevOps","System Architecture","Web Development","[Ben Basil Tomy]"],authors:[{name:"[Ben Basil Tomy]"}],creator:"[Ben Basil Tomy]",openGraph:{title:"[Ben Basil Tomy] - Full-Stack Developer Portfolio",description:"Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack.",url:"https://your-domain.com",siteName:"[Ben Basil Tomy] - Portfolio",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"[Ben Basil Tomy] - Full-Stack Developer Portfolio"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"[Ben Basil Tomy] - Full-Stack Developer",description:"Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack.",creator:"@yourusername",images:["/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function u({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${s().variable} ${o().variable} antialiased`,children:e})})}},5104:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n});let n=(0,i(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Portfolio\\\\devportfoliotemplates\\\\full-stack-developer-portfolio-template\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\page.tsx","default")},5852:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,i){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var r={},s=t.split(n),a=(i||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var h=l.substr(0,u).trim(),c=l.substr(++u,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==r[h]&&(r[h]=function(e,t){try{return t(e)}catch(t){return e}}(c,a))}}return r},t.serialize=function(e,t,n){var s=n||{},a=s.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!r.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!r.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!r.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,i=encodeURIComponent,n=/; */,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},8577:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var i=function(e){for(var t=[],i=0;i<e.length;){var n=e[i];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:i,value:e[i++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:i++,value:e[i++]});continue}if("{"===n){t.push({type:"OPEN",index:i,value:e[i++]});continue}if("}"===n){t.push({type:"CLOSE",index:i,value:e[i++]});continue}if(":"===n){for(var r="",s=i+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){r+=e[s++];continue}break}if(!r)throw TypeError("Missing parameter name at "+i);t.push({type:"NAME",index:i,value:r}),i=s;continue}if("("===n){var o=1,l="",s=i+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);t.push({type:"PATTERN",index:i,value:l}),i=s;continue}t.push({type:"CHAR",index:i,value:e[i++]})}return t.push({type:"END",index:i,value:""}),t}(e),n=t.prefixes,s=void 0===n?"./":n,a="[^"+r(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,h="",c=function(e){if(u<i.length&&i[u].type===e)return i[u++].value},d=function(e){var t=c(e);if(void 0!==t)return t;var n=i[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=c("CHAR")||c("ESCAPED_CHAR");)t+=e;return t};u<i.length;){var f=c("CHAR"),m=c("NAME"),g=c("PATTERN");if(m||g){var y=f||"";-1===s.indexOf(y)&&(h+=y,y=""),h&&(o.push(h),h=""),o.push({name:m||l++,prefix:y,suffix:"",pattern:g||a,modifier:c("MODIFIER")||""});continue}var v=f||c("ESCAPED_CHAR");if(v){h+=v;continue}if(h&&(o.push(h),h=""),c("OPEN")){var y=p(),x=c("NAME")||"",b=c("PATTERN")||"",w=p();d("CLOSE"),o.push({name:x||(b?l++:""),pattern:x&&!b?a:b,prefix:y,suffix:w,modifier:c("MODIFIER")||""});continue}d("END")}return o}function i(e,t){void 0===t&&(t={});var i=s(t),n=t.encode,r=void 0===n?function(e){return e}:n,a=t.validate,o=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",i)});return function(t){for(var i="",n=0;n<e.length;n++){var s=e[n];if("string"==typeof s){i+=s;continue}var a=t?t[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,h="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!h)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var c=0;c<a.length;c++){var d=r(a[c],s);if(o&&!l[n].test(d))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+d+'"');i+=s.prefix+d+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var d=r(String(a),s);if(o&&!l[n].test(d))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+d+'"');i+=s.prefix+d+s.suffix;continue}if(!u){var p=h?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+p)}}return i}}function n(e,t,i){void 0===i&&(i={});var n=i.decode,r=void 0===n?function(e){return e}:n;return function(i){var n=e.exec(i);if(!n)return!1;for(var s=n[0],a=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var i=t[e-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=n[e].split(i.prefix+i.suffix).map(function(e){return r(e,i)}):o[i.name]=r(n[e],i)}}(l);return{path:s,index:a,params:o}}}function r(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function a(e,t,i){void 0===i&&(i={});for(var n=i.strict,a=void 0!==n&&n,o=i.start,l=i.end,u=i.encode,h=void 0===u?function(e){return e}:u,c="["+r(i.endsWith||"")+"]|$",d="["+r(i.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)p+=r(h(m));else{var g=r(h(m.prefix)),y=r(h(m.suffix));if(m.pattern){if(t&&t.push(m),g||y){if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";p+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+v}else p+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier}else p+="("+m.pattern+")"+m.modifier}else p+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)a||(p+=d+"?"),p+=i.endsWith?"(?="+c+")":"$";else{var x=e[e.length-1],b="string"==typeof x?d.indexOf(x[x.length-1])>-1:void 0===x;a||(p+="(?:"+d+"(?="+c+"))?"),b||(p+="(?="+d+"|"+c+")")}return new RegExp(p,s(i))}function o(t,i,n){return t instanceof RegExp?function(e,t){if(!t)return e;var i=e.source.match(/\((?!\?)/g);if(i)for(var n=0;n<i.length;n++)t.push({name:n,prefix:"",suffix:"",modifier:"",pattern:""});return e}(t,i):Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,i,n).source}).join("|")+")",s(n)):a(e(t,n),i,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return i(e(t,n),n)},t.tokensToFunction=i,t.match=function(e,t){var i=[];return n(o(e,i,t),i,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=o})(),e.exports=t})()},8077:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return f},normalizeMetadataRoute:function(){return p}});let n=i(9177),r=function(e){return e&&e.__esModule?e:{default:e}}(i(8130)),s=i(8654),a=i(3960),o=i(3171),l=i(2045),u=i(8977),h=i(8758);function c(e){let t=r.default.dirname(e);if(e.endsWith("/sitemap"))return"";let i="";return t.split("/").some(e=>(0,h.isGroupSegment)(e)||(0,h.isParallelRouteSegment)(e))&&(i=(0,o.djb2Hash)(t).toString(36).slice(0,6)),i}function d(e,t,i){let n=(0,l.normalizeAppPath)(e),o=(0,a.getNamedRouteRegex)(n,!1),h=(0,s.interpolateDynamicPath)(n,t,o),{name:d,ext:p}=r.default.parse(i),f=c(r.default.posix.join(e,d)),m=f?`-${f}`:"";return(0,u.normalizePathSep)(r.default.join(h,`${d}${m}${p}`))}function p(e){if(!(0,n.isMetadataRoute)(e))return e;let t=e,i="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":i=c(e),!t.endsWith("/route")){let{dir:e,name:n,ext:s}=r.default.parse(t);t=r.default.posix.join(e,`${n}${i?`-${i}`:""}${s}`,"route")}return t}function f(e,t){let i=e.endsWith("/route"),n=i?e.slice(0,-6):e,r=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${r}`)+(i?"/route":"")}},9177:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{STATIC_METADATA_IMAGES:function(){return r},getExtensionRegexString:function(){return a},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return o},isStaticMetadataRoute:function(){return u},isStaticMetadataRouteFile:function(){return l}});let n=i(8977),r={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],a=(e,t)=>t?`(?:\\.(${e.join("|")})|((\\[\\])?\\.(${t.join("|")})))`:`\\.(?:${e.join("|")})`;function o(e,t,i){let s=[RegExp(`^[\\\\/]robots${i?`${a(t.concat("txt"),null)}$`:""}`),RegExp(`^[\\\\/]manifest${i?`${a(t.concat("webmanifest","json"),null)}$`:""}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${i?`${a(["xml"],t)}$`:""}`),RegExp(`[\\\\/]${r.icon.filename}\\d?${i?`${a(r.icon.extensions,t)}$`:""}`),RegExp(`[\\\\/]${r.apple.filename}\\d?${i?`${a(r.apple.extensions,t)}$`:""}`),RegExp(`[\\\\/]${r.openGraph.filename}\\d?${i?`${a(r.openGraph.extensions,t)}$`:""}`),RegExp(`[\\\\/]${r.twitter.filename}\\d?${i?`${a(r.twitter.extensions,t)}$`:""}`)],o=(0,n.normalizePathSep)(e);return s.some(e=>e.test(o))}function l(e){return o(e,[],!0)}function u(e){return"/robots"===e||"/manifest"===e||l(e)}function h(e){let t=e.replace(/^\/?app\//,"").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),!t.endsWith("/page")&&o(t,s,!1)}},4713:(e,t,i)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=i(5852);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},2828:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return r},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let n=i(2045),r=["(..)(..)","(.)","(..)","(...)"];function s(e){return void 0!==e.split("/").find(e=>r.find(t=>e.startsWith(t)))}function a(e){let t,i,s;for(let n of e.split("/"))if(i=r.find(e=>n.startsWith(e))){[t,s]=e.split(i,2);break}if(!t||!i||!s)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),i){case"(.)":s="/"===t?`/${s}`:t+"/"+s;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);s=t.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);s=a.slice(0,-2).concat(s).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:s}}},8654:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getUtils:function(){return m},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return f},normalizeVercelUrl:function(){return d}});let n=i(9551),r=i(9160),s=i(5296),a=i(3960),o=i(7073),l=i(8469),u=i(5e3),h=i(2045),c=i(2216);function d(e,t,i,r,s){if(r&&t&&s){let t=(0,n.parse)(e.url,!0);for(let e of(delete t.search,Object.keys(t.query))){let n=e!==c.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(c.NEXT_QUERY_PARAM_PREFIX),r=e!==c.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(c.NEXT_INTERCEPTION_MARKER_PREFIX);(n||r||(i||Object.keys(s.groups)).includes(e))&&delete t.query[e]}e.url=(0,n.format)(t)}}function p(e,t,i){if(!i)return e;for(let n of Object.keys(i.groups)){let r;let{optional:s,repeat:a}=i.groups[n],o=`[${a?"...":""}${n}]`;s&&(o=`[${o}]`);let l=t[n];r=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,r)}return e}function f(e,t,i,n){let r=!0;return i?{params:e=Object.keys(i.groups).reduce((s,a)=>{let o=e[a];"string"==typeof o&&(o=(0,h.normalizeRscURL)(o)),Array.isArray(o)&&(o=o.map(e=>("string"==typeof e&&(e=(0,h.normalizeRscURL)(e)),e)));let l=n[a],u=i.groups[a].optional;return((Array.isArray(l)?l.some(e=>Array.isArray(o)?o.some(t=>t.includes(e)):null==o?void 0:o.includes(e)):null==o?void 0:o.includes(l))||void 0===o&&!(u&&t))&&(r=!1),u&&(!o||Array.isArray(o)&&1===o.length&&("index"===o[0]||o[0]===`[[...${a}]]`))&&(o=void 0,delete e[a]),o&&"string"==typeof o&&i.groups[a].repeat&&(o=o.split("/")),o&&(s[a]=o),s},{}),hasValidParams:r}:{params:e,hasValidParams:!1}}function m({page:e,i18n:t,basePath:i,rewrites:n,pageIsDynamic:h,trailingSlash:m,caseSensitive:g}){let y,v,x;return h&&(y=(0,a.getNamedRouteRegex)(e,!1),x=(v=(0,o.getRouteMatcher)(y))(e)),{handleRewrites:function(a,o){let c={},d=o.pathname,p=n=>{let u=(0,s.getPathMatch)(n.source+(m?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g})(o.pathname);if((n.has||n.missing)&&u){let e=(0,l.matchHas)(a,o.query,n.has,n.missing);e?Object.assign(u,e):u=!1}if(u){let{parsedDestination:s,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:u,query:o.query});if(s.protocol)return!0;if(Object.assign(c,a,u),Object.assign(o.query,s.query),delete s.query,Object.assign(o,s),d=o.pathname,i&&(d=d.replace(RegExp(`^${i}`),"")||"/"),t){let e=(0,r.normalizeLocalePath)(d,t.locales);d=e.pathname,o.query.nextInternalLocale=e.detectedLocale||u.nextInternalLocale}if(d===e)return!0;if(h&&v){let e=v(d);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(d!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(d||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return c},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(e,i,n){return(0,o.getRouteMatcher)(function(){let{groups:e,routeKeys:r}=y;return{re:{exec:s=>{let a=Object.fromEntries(new URLSearchParams(s)),o=t&&n&&a["1"]===n;for(let e of Object.keys(a)){let t=a[e];e!==c.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(c.NEXT_QUERY_PARAM_PREFIX)&&(a[e.substring(c.NEXT_QUERY_PARAM_PREFIX.length)]=t,delete a[e])}let l=Object.keys(r||{}),u=e=>{if(t){let r=Array.isArray(e),s=r?e[0]:e;if("string"==typeof s&&t.locales.some(e=>e.toLowerCase()===s.toLowerCase()&&(n=e,i.locale=n,!0)))return r&&e.splice(0,1),!r||0===e.length}return!1};return l.every(e=>a[e])?l.reduce((t,i)=>{let n=null==r?void 0:r[i];return n&&!u(a[i])&&(t[e[n].pos]=a[i]),t},{}):Object.keys(a).reduce((e,t)=>{if(!u(a[t])){let i=t;return o&&(i=parseInt(t,10)-1+""),Object.assign(e,{[i]:a[t]})}return e},{})}},groups:e}}())(e.headers["x-now-route-matches"])},normalizeDynamicRouteParams:(e,t)=>f(e,t,y,x),normalizeVercelUrl:(e,t,i)=>d(e,t,i,h,y),interpolateDynamicPath:(e,t)=>p(e,t,y)}}},620:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return r}});let i=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function r(e){return i.test(e)?e.replace(n,"\\$&"):e}},3171:(e,t)=>{"use strict";function i(e){let t=5381;for(let i=0;i<e.length;i++)t=(t<<5)+t+e.charCodeAt(i)&0xffffffff;return t>>>0}function n(e){return i(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{djb2Hash:function(){return i},hexHash:function(){return n}})},164:(e,t)=>{"use strict";function i(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},8977:(e,t)=>{"use strict";function i(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return i}})},2045:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let n=i(164),r=i(8758);function s(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,i,n)=>!t||(0,r.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&i===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},1089:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return r}}),i(1706);let n=i(6678);function r(e,t,i){void 0===i&&(i=!0);let r=new URL("http://n"),s=t?new URL(t,r):e.startsWith(".")?new URL("http://n"):r,{pathname:a,searchParams:o,search:l,hash:u,href:h,origin:c}=new URL(e,s);if(c!==r.origin)throw Error("invariant: invalid relative URL, router received "+e);return{pathname:a,query:i?(0,n.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:h.slice(c.length)}}},7600:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return s}});let n=i(6678),r=i(1089);function s(e){if(e.startsWith("/"))return(0,r.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},5296:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return r}});let n=i(8577);function r(e,t){let i=[],r=(0,n.pathToRegexp)(e,i,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),s=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(r.source),r.flags):r,i);return(e,n)=>{if("string"!=typeof e)return!1;let r=s(e);if(!r)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of i)"number"==typeof e.name&&delete r.params[e.name];return{...n,...r.params}}}},8469:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{compileNonPath:function(){return c},matchHas:function(){return h},prepareDestination:function(){return d}});let n=i(8577),r=i(620),s=i(7600),a=i(2828),o=i(484),l=i(4713);function u(e){return e.replace(/__ESC_COLON_/gi,":")}function h(e,t,i,n){void 0===i&&(i=[]),void 0===n&&(n=[]);let r={},s=i=>{let n;let s=i.key;switch(i.type){case"header":s=s.toLowerCase(),n=e.headers[s];break;case"cookie":n="cookies"in e?e.cookies[i.key]:(0,l.getCookieParser)(e.headers)()[i.key];break;case"query":n=t[s];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!i.value&&n)return r[function(e){let t="";for(let i=0;i<e.length;i++){let n=e.charCodeAt(i);(n>64&&n<91||n>96&&n<123)&&(t+=e[i])}return t}(s)]=n,!0;if(n){let e=RegExp("^"+i.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{r[e]=t.groups[e]}):"host"===i.type&&t[0]&&(r.host=t[0])),!0}return!1};return!!i.every(e=>s(e))&&!n.some(e=>s(e))&&r}function c(e,t){if(!e.includes(":"))return e;for(let i of Object.keys(t))e.includes(":"+i)&&(e=e.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t;let i=Object.assign({},e.query);delete i.__nextLocale,delete i.__nextDefaultLocale,delete i.__nextDataReq,delete i.__nextInferredLocaleFromDefault,delete i[o.NEXT_RSC_UNION_QUERY];let l=e.destination;for(let t of Object.keys({...e.params,...i}))l=t?l.replace(RegExp(":"+(0,r.escapeStringRegexp)(t),"g"),"__ESC_COLON_"+t):l;let h=(0,s.parseUrl)(l),d=h.query,p=u(""+h.pathname+(h.hash||"")),f=u(h.hostname||""),m=[],g=[];(0,n.pathToRegexp)(p,m),(0,n.pathToRegexp)(f,g);let y=[];m.forEach(e=>y.push(e.name)),g.forEach(e=>y.push(e.name));let v=(0,n.compile)(p,{validate:!1}),x=(0,n.compile)(f,{validate:!1});for(let[t,i]of Object.entries(d))Array.isArray(i)?d[t]=i.map(t=>c(u(t),e.params)):"string"==typeof i&&(d[t]=c(u(i),e.params));let b=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!b.some(e=>y.includes(e)))for(let t of b)t in d||(d[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(p))for(let t of p.split("/")){let i=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(i){"(..)(..)"===i?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=i;break}}try{let[i,n]=(t=v(e.params)).split("#",2);h.hostname=x(e.params),h.pathname=i,h.hash=(n?"#":"")+(n||""),delete h.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match");throw e}return h.query={...i,...h.query},{newUrl:t,destQuery:d,parsedDestination:h}}},6678:(e,t)=>{"use strict";function i(e){let t={};return e.forEach((e,i)=>{void 0===t[i]?t[i]=e:Array.isArray(t[i])?t[i].push(e):t[i]=[t[i],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function r(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[i,r]=e;Array.isArray(r)?r.forEach(e=>t.append(i,n(e))):t.set(i,n(r))}),t}function s(e){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return i.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,i)=>e.append(i,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},7073:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return r}});let n=i(1706);function r(e){let{re:t,groups:i}=e;return e=>{let r=t.exec(e);if(!r)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},a={};return Object.keys(i).forEach(e=>{let t=i[e],n=r[t.pos];void 0!==n&&(a[e]=~n.indexOf("/")?n.split("/").map(e=>s(e)):t.repeat?[s(n)]:s(n))}),a}}},3960:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return c},parseParameter:function(){return l}});let n=i(2216),r=i(2828),s=i(620),a=i(5e3),o=/\[((?:\[.*\])|.+)\]/;function l(e){let t=e.match(o);return t?u(t[1]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let i=e.startsWith("...");return i&&(e=e.slice(3)),{key:e,repeat:i,optional:t}}function h(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),i={},n=1;return{parameterizedRoute:t.map(e=>{let t=r.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(o);if(t&&a){let{key:e,optional:r,repeat:o}=u(a[1]);return i[e]={pos:n++,repeat:o,optional:r},"/"+(0,s.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,s.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:r}=u(a[1]);return i[e]={pos:n++,repeat:t,optional:r},t?r?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:i}}function c(e){let{parameterizedRoute:t,groups:i}=h(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:i}}function d(e){let{interceptionMarker:t,getSafeRouteKey:i,segment:n,routeKeys:r,keyPrefix:a}=e,{key:o,optional:l,repeat:h}=u(n),c=o.replace(/\W/g,"");a&&(c=""+a+c);let d=!1;(0===c.length||c.length>30)&&(d=!0),isNaN(parseInt(c.slice(0,1)))||(d=!0),d&&(c=i()),a?r[c]=""+a+o:r[c]=o;let p=t?(0,s.escapeStringRegexp)(t):"";return h?l?"(?:/"+p+"(?<"+c+">.+?))?":"/"+p+"(?<"+c+">.+?)":"/"+p+"(?<"+c+">[^/]+?)"}function p(e,t){let i;let o=(0,a.removeTrailingSlash)(e).slice(1).split("/"),l=(i=0,()=>{let e="",t=++i;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),u={};return{namedParameterizedRoute:o.map(e=>{let i=r.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(i&&a){let[i]=e.split(a[0]);return d({getSafeRouteKey:l,interceptionMarker:i,segment:a[1],routeKeys:u,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return a?d({getSafeRouteKey:l,segment:a[1],routeKeys:u,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,s.escapeStringRegexp)(e)}).join(""),routeKeys:u}}function f(e,t){let i=p(e,t);return{...c(e),namedRegex:"^"+i.namedParameterizedRoute+"(?:/)?$",routeKeys:i.routeKeys}}function m(e,t){let{parameterizedRoute:i}=h(e),{catchAll:n=!0}=t;if("/"===i)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:r}=p(e,!1);return{namedRegex:"^"+r+(n?"(?:(/.*)?)":"")+"$"}}},1706:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,t=e(...r)),t}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>r.test(e);function a(){let{protocol:e,hostname:t,port:i}=window.location;return e+"//"+t+(i?":"+i:"")}function o(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function h(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function c(e,t){let i=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await c(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(i&&u(i))return n;if(!n)throw Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},6055:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});var n=i(8077);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},2704:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),n=t.X(0,[638,949],()=>i(5326));module.exports=n})();