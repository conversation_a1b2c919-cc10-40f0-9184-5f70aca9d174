(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>p,routeModule:()=>u,tree:()=>d});var o=r(260),n=r(8203),s=r(5155),i=r.n(s),a=r(7292),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,9937,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9611)),"C:\\Users\\<USER>\\Portfolio\\devportfoliotemplates\\full-stack-developer-portfolio-template\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,1485,23)),"next/dist/client/components/unauthorized-error"]}],p=[],m={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8375:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3219,23)),Promise.resolve().then(r.t.bind(r,4863,23)),Promise.resolve().then(r.t.bind(r,5155,23)),Promise.resolve().then(r.t.bind(r,802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,8530,23)),Promise.resolve().then(r.t.bind(r,8921,23))},3103:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6959,23)),Promise.resolve().then(r.t.bind(r,3875,23)),Promise.resolve().then(r.t.bind(r,8903,23)),Promise.resolve().then(r.t.bind(r,7174,23)),Promise.resolve().then(r.t.bind(r,4178,23)),Promise.resolve().then(r.t.bind(r,7190,23)),Promise.resolve().then(r.t.bind(r,1365,23))},9375:()=>{},6327:()=>{},9611:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var o=r(2740),n=r(7879),s=r.n(n),i=r(3298),a=r.n(i);r(2704);let l={title:"[Ben Basil Tomy] - Software Engineer",description:"Welcome to my portfolio! I am a passionate full-stack developer who bridges the gap between frontend and backend development. Specializing in creating complete web solutions, from beautiful user interfaces to robust server architectures.",keywords:["Full-Stack Developer","Software Engineer","Frontend Development","Backend Development","React","Next.js","Node.js","TypeScript","Database Design","API Development","Cloud Solutions","DevOps","System Architecture","Web Development","[Ben Basil Tomy]"],authors:[{name:"[Ben Basil Tomy]"}],creator:"[Ben Basil Tomy]",openGraph:{title:"[Ben Basil Tomy] - Full-Stack Developer Portfolio",description:"Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack.",url:"https://your-domain.com",siteName:"[Ben Basil Tomy] - Portfolio",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"[Ben Basil Tomy] - Full-Stack Developer Portfolio"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"[Ben Basil Tomy] - Full-Stack Developer",description:"Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack.",creator:"@yourusername",images:["/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function d({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${s().variable} ${a().variable} antialiased`,children:e})})}},2704:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[638,949],()=>r(6354));module.exports=o})();